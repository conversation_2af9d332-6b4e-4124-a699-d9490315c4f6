<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Serverless\V1\Service;

use <PERSON><PERSON><PERSON>\Options;
use <PERSON>wi<PERSON>\Values;

/**
 * PLEASE NOTE that this class contains beta products that are subject to change. Use them with caution.
 */
abstract class EnvironmentOptions {
    /**
     * @param string $domainSuffix A URL-friendly name that represents the
     *                             environment
     * @return CreateEnvironmentOptions Options builder
     */
    public static function create(string $domainSuffix = Values::NONE): CreateEnvironmentOptions {
        return new CreateEnvironmentOptions($domainSuffix);
    }
}

class CreateEnvironmentOptions extends Options {
    /**
     * @param string $domainSuffix A URL-friendly name that represents the
     *                             environment
     */
    public function __construct(string $domainSuffix = Values::NONE) {
        $this->options['domainSuffix'] = $domainSuffix;
    }

    /**
     * A URL-friendly name that represents the environment and forms part of the domain name. It can be a maximum of 16 characters.
     *
     * @param string $domainSuffix A URL-friendly name that represents the
     *                             environment
     * @return $this Fluent Builder
     */
    public function setDomainSuffix(string $domainSuffix): self {
        $this->options['domainSuffix'] = $domainSuffix;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Serverless.V1.CreateEnvironmentOptions ' . $options . ']';
    }
}