<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Serverless\V1\Service\Environment;

use Twilio\Options;
use Twilio\Values;

/**
 * PLEASE NOTE that this class contains beta products that are subject to change. Use them with caution.
 */
abstract class DeploymentOptions {
    /**
     * @param string $buildSid The SID of the Build for the Deployment
     * @return CreateDeploymentOptions Options builder
     */
    public static function create(string $buildSid = Values::NONE): CreateDeploymentOptions {
        return new CreateDeploymentOptions($buildSid);
    }
}

class CreateDeploymentOptions extends Options {
    /**
     * @param string $buildSid The SID of the Build for the Deployment
     */
    public function __construct(string $buildSid = Values::NONE) {
        $this->options['buildSid'] = $buildSid;
    }

    /**
     * The SID of the Build for the Deployment.
     *
     * @param string $buildSid The SID of the Build for the Deployment
     * @return $this Fluent Builder
     */
    public function setBuildSid(string $buildSid): self {
        $this->options['buildSid'] = $buildSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Serverless.V1.CreateDeploymentOptions ' . $options . ']';
    }
}