<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Serverless\V1\Service\TwilioFunction;

use <PERSON><PERSON><PERSON>\Deserialize;
use T<PERSON>lio\Exceptions\TwilioException;
use Twilio\InstanceResource;
use Twilio\Rest\Serverless\V1\Service\TwilioFunction\FunctionVersion\FunctionVersionContentList;
use Twilio\Values;
use Twilio\Version;

/**
 * PLEASE NOTE that this class contains beta products that are subject to change. Use them with caution.
 *
 * @property string $sid
 * @property string $accountSid
 * @property string $serviceSid
 * @property string $functionSid
 * @property string $path
 * @property string $visibility
 * @property \DateTime $dateCreated
 * @property string $url
 * @property array $links
 */
class FunctionVersionInstance extends InstanceResource {
    protected $_functionVersionContent;

    /**
     * Initialize the FunctionVersionInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $serviceSid The SID of the Service that the Function Version
     *                           resource is associated with
     * @param string $functionSid The SID of the Function resource that is the
     *                            parent of the Function Version resource
     * @param string $sid The SID that identifies the Function Version resource to
     *                    fetch
     */
    public function __construct(Version $version, array $payload, string $serviceSid, string $functionSid, string $sid = null) {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'serviceSid' => Values::array_get($payload, 'service_sid'),
            'functionSid' => Values::array_get($payload, 'function_sid'),
            'path' => Values::array_get($payload, 'path'),
            'visibility' => Values::array_get($payload, 'visibility'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'url' => Values::array_get($payload, 'url'),
            'links' => Values::array_get($payload, 'links'),
        ];

        $this->solution = [
            'serviceSid' => $serviceSid,
            'functionSid' => $functionSid,
            'sid' => $sid ?: $this->properties['sid'],
        ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return FunctionVersionContext Context for this FunctionVersionInstance
     */
    protected function proxy(): FunctionVersionContext {
        if (!$this->context) {
            $this->context = new FunctionVersionContext(
                $this->version,
                $this->solution['serviceSid'],
                $this->solution['functionSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the FunctionVersionInstance
     *
     * @return FunctionVersionInstance Fetched FunctionVersionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FunctionVersionInstance {
        return $this->proxy()->fetch();
    }

    /**
     * Access the functionVersionContent
     */
    protected function getFunctionVersionContent(): FunctionVersionContentList {
        return $this->proxy()->functionVersionContent;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name) {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Serverless.V1.FunctionVersionInstance ' . \implode(' ', $context) . ']';
    }
}