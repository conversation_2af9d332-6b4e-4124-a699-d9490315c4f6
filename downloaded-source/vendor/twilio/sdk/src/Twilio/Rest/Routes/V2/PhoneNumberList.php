<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Routes\V2;

use Twilio\ListResource;
use Twilio\Version;

class PhoneNumberList extends ListResource {
    /**
     * Construct the PhoneNumberList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(Version $version) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [];
    }

    /**
     * Constructs a PhoneNumberContext
     *
     * @param string $phoneNumber The phone number
     */
    public function getContext(string $phoneNumber): PhoneNumberContext {
        return new PhoneNumberContext($this->version, $phoneNumber);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Routes.V2.PhoneNumberList]';
    }
}