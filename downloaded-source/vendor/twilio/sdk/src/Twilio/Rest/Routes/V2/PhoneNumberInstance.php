<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Routes\V2;

use <PERSON>wi<PERSON>\Deserialize;
use T<PERSON><PERSON>\Exceptions\TwilioException;
use T<PERSON>lio\InstanceResource;
use Twilio\Options;
use Twilio\Values;
use Twilio\Version;

/**
 * @property string $phoneNumber
 * @property string $url
 * @property string $sid
 * @property string $accountSid
 * @property string $friendlyName
 * @property string $voiceRegion
 * @property \DateTime $dateCreated
 * @property \DateTime $dateUpdated
 */
class PhoneNumberInstance extends InstanceResource {
    /**
     * Initialize the PhoneNumberInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $phoneNumber The phone number
     */
    public function __construct(Version $version, array $payload, string $phoneNumber = null) {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'phoneNumber' => Values::array_get($payload, 'phone_number'),
            'url' => Values::array_get($payload, 'url'),
            'sid' => Values::array_get($payload, 'sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'friendlyName' => Values::array_get($payload, 'friendly_name'),
            'voiceRegion' => Values::array_get($payload, 'voice_region'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
        ];

        $this->solution = ['phoneNumber' => $phoneNumber ?: $this->properties['phoneNumber'], ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return PhoneNumberContext Context for this PhoneNumberInstance
     */
    protected function proxy(): PhoneNumberContext {
        if (!$this->context) {
            $this->context = new PhoneNumberContext($this->version, $this->solution['phoneNumber']);
        }

        return $this->context;
    }

    /**
     * Update the PhoneNumberInstance
     *
     * @param array|Options $options Optional Arguments
     * @return PhoneNumberInstance Updated PhoneNumberInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(array $options = []): PhoneNumberInstance {
        return $this->proxy()->update($options);
    }

    /**
     * Fetch the PhoneNumberInstance
     *
     * @return PhoneNumberInstance Fetched PhoneNumberInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): PhoneNumberInstance {
        return $this->proxy()->fetch();
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name) {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Routes.V2.PhoneNumberInstance ' . \implode(' ', $context) . ']';
    }
}