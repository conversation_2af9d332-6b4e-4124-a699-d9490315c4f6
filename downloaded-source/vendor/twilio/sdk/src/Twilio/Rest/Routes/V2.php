<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Routes;

use Twilio\Domain;
use Twilio\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\InstanceContext;
use Twilio\Rest\Routes\V2\PhoneNumberList;
use Twilio\Rest\Routes\V2\SipDomainList;
use Twilio\Rest\Routes\V2\TrunkList;
use Twilio\Version;

/**
 * @property PhoneNumberList $phoneNumbers
 * @property SipDomainList $sipDomains
 * @property TrunkList $trunks
 * @method \Twilio\Rest\Routes\V2\PhoneNumberContext phoneNumbers(string $phoneNumber)
 * @method \Twilio\Rest\Routes\V2\SipDomainContext sipDomains(string $sipDomain)
 * @method \Twilio\Rest\Routes\V2\TrunkContext trunks(string $sipTrunkDomain)
 */
class V2 extends Version {
    protected $_phoneNumbers;
    protected $_sipDomains;
    protected $_trunks;

    /**
     * Construct the V2 version of Routes
     *
     * @param Domain $domain Domain that contains the version
     */
    public function __construct(Domain $domain) {
        parent::__construct($domain);
        $this->version = 'v2';
    }

    protected function getPhoneNumbers(): PhoneNumberList {
        if (!$this->_phoneNumbers) {
            $this->_phoneNumbers = new PhoneNumberList($this);
        }
        return $this->_phoneNumbers;
    }

    protected function getSipDomains(): SipDomainList {
        if (!$this->_sipDomains) {
            $this->_sipDomains = new SipDomainList($this);
        }
        return $this->_sipDomains;
    }

    protected function getTrunks(): TrunkList {
        if (!$this->_trunks) {
            $this->_trunks = new TrunkList($this);
        }
        return $this->_trunks;
    }

    /**
     * Magic getter to lazy load root resources
     *
     * @param string $name Resource to return
     * @return \Twilio\ListResource The requested resource
     * @throws TwilioException For unknown resource
     */
    public function __get(string $name) {
        $method = 'get' . \ucfirst($name);
        if (\method_exists($this, $method)) {
            return $this->$method();
        }

        throw new TwilioException('Unknown resource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Routes.V2]';
    }
}