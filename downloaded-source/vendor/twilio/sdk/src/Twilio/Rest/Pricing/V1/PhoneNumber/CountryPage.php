<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Pricing\V1\PhoneNumber;

use Twilio\Http\Response;
use <PERSON><PERSON><PERSON>\Page;
use Twilio\Version;

class CountryPage extends Page {
    /**
     * @param Version $version Version that contains the resource
     * @param Response $response Response from the API
     * @param array $solution The context solution
     */
    public function __construct(Version $version, Response $response, array $solution) {
        parent::__construct($version, $response);

        // Path Solution
        $this->solution = $solution;
    }

    /**
     * @param array $payload Payload response from the API
     * @return CountryInstance \Twilio\Rest\Pricing\V1\PhoneNumber\CountryInstance
     */
    public function buildInstance(array $payload): CountryInstance {
        return new CountryInstance($this->version, $payload);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Pricing.V1.CountryPage]';
    }
}