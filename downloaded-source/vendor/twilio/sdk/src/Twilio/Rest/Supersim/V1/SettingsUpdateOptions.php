<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Supersim\V1;

use <PERSON><PERSON><PERSON>\Options;
use <PERSON>wi<PERSON>\Values;

/**
 * PLEASE NOTE that this class contains beta products that are subject to change. Use them with caution.
 */
abstract class SettingsUpdateOptions {
    /**
     * @param string $sim Filter the Settings Updates by Super SIM
     * @param string $status Filter the Settings Updates by status
     * @return ReadSettingsUpdateOptions Options builder
     */
    public static function read(string $sim = Values::NONE, string $status = Values::NONE): ReadSettingsUpdateOptions {
        return new ReadSettingsUpdateOptions($sim, $status);
    }
}

class ReadSettingsUpdateOptions extends Options {
    /**
     * @param string $sim Filter the Settings Updates by Super SIM
     * @param string $status Filter the Settings Updates by status
     */
    public function __construct(string $sim = Values::NONE, string $status = Values::NONE) {
        $this->options['sim'] = $sim;
        $this->options['status'] = $status;
    }

    /**
     * Filter the Settings Updates by a Super SIM's SID or UniqueName.
     *
     * @param string $sim Filter the Settings Updates by Super SIM
     * @return $this Fluent Builder
     */
    public function setSim(string $sim): self {
        $this->options['sim'] = $sim;
        return $this;
    }

    /**
     * Filter the Settings Updates by status. Can be `scheduled`, `in-progress`, `successful`, or `failed`.
     *
     * @param string $status Filter the Settings Updates by status
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Supersim.V1.ReadSettingsUpdateOptions ' . $options . ']';
    }
}