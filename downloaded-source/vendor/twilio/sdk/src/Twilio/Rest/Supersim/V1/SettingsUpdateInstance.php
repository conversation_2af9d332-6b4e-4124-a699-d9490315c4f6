<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Supersim\V1;

use <PERSON>wi<PERSON>\Deserialize;
use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\InstanceResource;
use Twilio\Values;
use Twilio\Version;

/**
 * PLEASE NOTE that this class contains beta products that are subject to change. Use them with caution.
 *
 * @property string $sid
 * @property string $iccid
 * @property string $simSid
 * @property string $status
 * @property array[] $packages
 * @property \DateTime $dateCompleted
 * @property \DateTime $dateCreated
 * @property \DateTime $dateUpdated
 */
class SettingsUpdateInstance extends InstanceResource {
    /**
     * Initialize the SettingsUpdateInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     */
    public function __construct(Version $version, array $payload) {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'iccid' => Values::array_get($payload, 'iccid'),
            'simSid' => Values::array_get($payload, 'sim_sid'),
            'status' => Values::array_get($payload, 'status'),
            'packages' => Values::array_get($payload, 'packages'),
            'dateCompleted' => Deserialize::dateTime(Values::array_get($payload, 'date_completed')),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
        ];

        $this->solution = [];
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name) {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Supersim.V1.SettingsUpdateInstance]';
    }
}