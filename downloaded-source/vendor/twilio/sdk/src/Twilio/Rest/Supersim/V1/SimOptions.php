<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Supersim\V1;

use <PERSON><PERSON><PERSON>\Options;
use <PERSON>wi<PERSON>\Values;

/**
 * PLEASE NOTE that this class contains beta products that are subject to change. Use them with caution.
 */
abstract class SimOptions {
    /**
     * @param string $uniqueName An application-defined string that uniquely
     *                           identifies the resource
     * @param string $status The new status of the Super SIM
     * @param string $fleet The SID or unique name of the Fleet to which the SIM
     *                      resource should be assigned
     * @param string $callbackUrl The URL we should call after the update has
     *                            finished
     * @param string $callbackMethod The HTTP method we should use to call
     *                               callback_url
     * @param string $accountSid The SID of the Account to which the Sim resource
     *                           should belong
     * @return UpdateSimOptions Options builder
     */
    public static function update(string $uniqueName = Values::NONE, string $status = Values::NONE, string $fleet = Values::NONE, string $callbackUrl = Values::NONE, string $callbackMethod = Values::NONE, string $accountSid = Values::NONE): UpdateSimOptions {
        return new UpdateSimOptions($uniqueName, $status, $fleet, $callbackUrl, $callbackMethod, $accountSid);
    }

    /**
     * @param string $status The status of the Sim resources to read
     * @param string $fleet The SID or unique name of the Fleet to which a list of
     *                      Sims are assigned
     * @param string $iccid The ICCID associated with a Super SIM to filter the
     *                      list by
     * @return ReadSimOptions Options builder
     */
    public static function read(string $status = Values::NONE, string $fleet = Values::NONE, string $iccid = Values::NONE): ReadSimOptions {
        return new ReadSimOptions($status, $fleet, $iccid);
    }
}

class UpdateSimOptions extends Options {
    /**
     * @param string $uniqueName An application-defined string that uniquely
     *                           identifies the resource
     * @param string $status The new status of the Super SIM
     * @param string $fleet The SID or unique name of the Fleet to which the SIM
     *                      resource should be assigned
     * @param string $callbackUrl The URL we should call after the update has
     *                            finished
     * @param string $callbackMethod The HTTP method we should use to call
     *                               callback_url
     * @param string $accountSid The SID of the Account to which the Sim resource
     *                           should belong
     */
    public function __construct(string $uniqueName = Values::NONE, string $status = Values::NONE, string $fleet = Values::NONE, string $callbackUrl = Values::NONE, string $callbackMethod = Values::NONE, string $accountSid = Values::NONE) {
        $this->options['uniqueName'] = $uniqueName;
        $this->options['status'] = $status;
        $this->options['fleet'] = $fleet;
        $this->options['callbackUrl'] = $callbackUrl;
        $this->options['callbackMethod'] = $callbackMethod;
        $this->options['accountSid'] = $accountSid;
    }

    /**
     * An application-defined string that uniquely identifies the resource. It can be used in place of the resource's `sid` in the URL to address the resource.
     *
     * @param string $uniqueName An application-defined string that uniquely
     *                           identifies the resource
     * @return $this Fluent Builder
     */
    public function setUniqueName(string $uniqueName): self {
        $this->options['uniqueName'] = $uniqueName;
        return $this;
    }

    /**
     * The new status of the resource. Can be: `ready`, `active`, or `inactive`. See the [Super SIM Status Values](https://www.twilio.com/docs/iot/supersim/api/sim-resource#status-values) for more info.
     *
     * @param string $status The new status of the Super SIM
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * The SID or unique name of the Fleet to which the SIM resource should be assigned.
     *
     * @param string $fleet The SID or unique name of the Fleet to which the SIM
     *                      resource should be assigned
     * @return $this Fluent Builder
     */
    public function setFleet(string $fleet): self {
        $this->options['fleet'] = $fleet;
        return $this;
    }

    /**
     * The URL we should call using the `callback_method` after an asynchronous update has finished.
     *
     * @param string $callbackUrl The URL we should call after the update has
     *                            finished
     * @return $this Fluent Builder
     */
    public function setCallbackUrl(string $callbackUrl): self {
        $this->options['callbackUrl'] = $callbackUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `callback_url`. Can be: `GET` or `POST` and the default is POST.
     *
     * @param string $callbackMethod The HTTP method we should use to call
     *                               callback_url
     * @return $this Fluent Builder
     */
    public function setCallbackMethod(string $callbackMethod): self {
        $this->options['callbackMethod'] = $callbackMethod;
        return $this;
    }

    /**
     * The SID of the Account to which the Sim resource should belong. The Account SID can only be that of the requesting Account or that of a Subaccount of the requesting Account. Only valid when the Sim resource's status is new.
     *
     * @param string $accountSid The SID of the Account to which the Sim resource
     *                           should belong
     * @return $this Fluent Builder
     */
    public function setAccountSid(string $accountSid): self {
        $this->options['accountSid'] = $accountSid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Supersim.V1.UpdateSimOptions ' . $options . ']';
    }
}

class ReadSimOptions extends Options {
    /**
     * @param string $status The status of the Sim resources to read
     * @param string $fleet The SID or unique name of the Fleet to which a list of
     *                      Sims are assigned
     * @param string $iccid The ICCID associated with a Super SIM to filter the
     *                      list by
     */
    public function __construct(string $status = Values::NONE, string $fleet = Values::NONE, string $iccid = Values::NONE) {
        $this->options['status'] = $status;
        $this->options['fleet'] = $fleet;
        $this->options['iccid'] = $iccid;
    }

    /**
     * The status of the Sim resources to read. Can be `new`, `ready`, `active`, `inactive`, or `scheduled`.
     *
     * @param string $status The status of the Sim resources to read
     * @return $this Fluent Builder
     */
    public function setStatus(string $status): self {
        $this->options['status'] = $status;
        return $this;
    }

    /**
     * The SID or unique name of the Fleet to which a list of Sims are assigned.
     *
     * @param string $fleet The SID or unique name of the Fleet to which a list of
     *                      Sims are assigned
     * @return $this Fluent Builder
     */
    public function setFleet(string $fleet): self {
        $this->options['fleet'] = $fleet;
        return $this;
    }

    /**
     * The [ICCID](https://en.wikipedia.org/wiki/Subscriber_identity_module#ICCID) associated with a Super SIM to filter the list by. Passing this parameter will always return a list containing zero or one SIMs.
     *
     * @param string $iccid The ICCID associated with a Super SIM to filter the
     *                      list by
     * @return $this Fluent Builder
     */
    public function setIccid(string $iccid): self {
        $this->options['iccid'] = $iccid;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Supersim.V1.ReadSimOptions ' . $options . ']';
    }
}