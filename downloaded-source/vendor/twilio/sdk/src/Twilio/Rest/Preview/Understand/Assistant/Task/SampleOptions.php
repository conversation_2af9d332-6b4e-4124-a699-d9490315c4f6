<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Preview\Understand\Assistant\Task;

use <PERSON><PERSON><PERSON>\Options;
use <PERSON><PERSON><PERSON>\Values;

/**
 * PLEASE NOTE that this class contains preview products that are subject to change. Use them with caution. If you currently do not have developer preview access, <NAME_EMAIL>.
 */
abstract class SampleOptions {
    /**
     * @param string $language An ISO language-country string of the sample.
     * @return ReadSampleOptions Options builder
     */
    public static function read(string $language = Values::NONE): ReadSampleOptions {
        return new ReadSampleOptions($language);
    }

    /**
     * @param string $sourceChannel The communication channel the sample was
     *                              captured. It can be: voice, sms, chat, alexa,
     *                              google-assistant, or slack. If not included the
     *                              value will be null
     * @return CreateSampleOptions Options builder
     */
    public static function create(string $sourceChannel = Values::NONE): CreateSampleOptions {
        return new CreateSampleOptions($sourceChannel);
    }

    /**
     * @param string $language An ISO language-country string of the sample.
     * @param string $taggedText The text example of how end-users may express this
     *                           task. The sample may contain Field tag blocks.
     * @param string $sourceChannel The communication channel the sample was
     *                              captured. It can be: voice, sms, chat, alexa,
     *                              google-assistant, or slack. If not included the
     *                              value will be null
     * @return UpdateSampleOptions Options builder
     */
    public static function update(string $language = Values::NONE, string $taggedText = Values::NONE, string $sourceChannel = Values::NONE): UpdateSampleOptions {
        return new UpdateSampleOptions($language, $taggedText, $sourceChannel);
    }
}

class ReadSampleOptions extends Options {
    /**
     * @param string $language An ISO language-country string of the sample.
     */
    public function __construct(string $language = Values::NONE) {
        $this->options['language'] = $language;
    }

    /**
     * An ISO language-country string of the sample.
     *
     * @param string $language An ISO language-country string of the sample.
     * @return $this Fluent Builder
     */
    public function setLanguage(string $language): self {
        $this->options['language'] = $language;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Understand.ReadSampleOptions ' . $options . ']';
    }
}

class CreateSampleOptions extends Options {
    /**
     * @param string $sourceChannel The communication channel the sample was
     *                              captured. It can be: voice, sms, chat, alexa,
     *                              google-assistant, or slack. If not included the
     *                              value will be null
     */
    public function __construct(string $sourceChannel = Values::NONE) {
        $this->options['sourceChannel'] = $sourceChannel;
    }

    /**
     * The communication channel the sample was captured. It can be: *voice*, *sms*, *chat*, *alexa*, *google-assistant*, or *slack*. If not included the value will be null
     *
     * @param string $sourceChannel The communication channel the sample was
     *                              captured. It can be: voice, sms, chat, alexa,
     *                              google-assistant, or slack. If not included the
     *                              value will be null
     * @return $this Fluent Builder
     */
    public function setSourceChannel(string $sourceChannel): self {
        $this->options['sourceChannel'] = $sourceChannel;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Understand.CreateSampleOptions ' . $options . ']';
    }
}

class UpdateSampleOptions extends Options {
    /**
     * @param string $language An ISO language-country string of the sample.
     * @param string $taggedText The text example of how end-users may express this
     *                           task. The sample may contain Field tag blocks.
     * @param string $sourceChannel The communication channel the sample was
     *                              captured. It can be: voice, sms, chat, alexa,
     *                              google-assistant, or slack. If not included the
     *                              value will be null
     */
    public function __construct(string $language = Values::NONE, string $taggedText = Values::NONE, string $sourceChannel = Values::NONE) {
        $this->options['language'] = $language;
        $this->options['taggedText'] = $taggedText;
        $this->options['sourceChannel'] = $sourceChannel;
    }

    /**
     * An ISO language-country string of the sample.
     *
     * @param string $language An ISO language-country string of the sample.
     * @return $this Fluent Builder
     */
    public function setLanguage(string $language): self {
        $this->options['language'] = $language;
        return $this;
    }

    /**
     * The text example of how end-users may express this task. The sample may contain Field tag blocks.
     *
     * @param string $taggedText The text example of how end-users may express this
     *                           task. The sample may contain Field tag blocks.
     * @return $this Fluent Builder
     */
    public function setTaggedText(string $taggedText): self {
        $this->options['taggedText'] = $taggedText;
        return $this;
    }

    /**
     * The communication channel the sample was captured. It can be: *voice*, *sms*, *chat*, *alexa*, *google-assistant*, or *slack*. If not included the value will be null
     *
     * @param string $sourceChannel The communication channel the sample was
     *                              captured. It can be: voice, sms, chat, alexa,
     *                              google-assistant, or slack. If not included the
     *                              value will be null
     * @return $this Fluent Builder
     */
    public function setSourceChannel(string $sourceChannel): self {
        $this->options['sourceChannel'] = $sourceChannel;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Preview.Understand.UpdateSampleOptions ' . $options . ']';
    }
}