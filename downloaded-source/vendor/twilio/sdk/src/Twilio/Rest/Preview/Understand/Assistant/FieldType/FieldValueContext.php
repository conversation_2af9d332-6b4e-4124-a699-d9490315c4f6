<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Preview\Understand\Assistant\FieldType;

use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use <PERSON><PERSON>lio\InstanceContext;
use Twilio\Values;
use Twilio\Version;

/**
 * PLEASE NOTE that this class contains preview products that are subject to change. Use them with caution. If you currently do not have developer preview access, <NAME_EMAIL>.
 */
class FieldValueContext extends InstanceContext {
    /**
     * Initialize the FieldValueContext
     *
     * @param Version $version Version that contains the resource
     * @param string $assistantSid The assistant_sid
     * @param string $fieldTypeSid The field_type_sid
     * @param string $sid The sid
     */
    public function __construct(Version $version, $assistantSid, $fieldTypeSid, $sid) {
        parent::__construct($version);

        // Path Solution
        $this->solution = ['assistantSid' => $assistantSid, 'fieldTypeSid' => $fieldTypeSid, 'sid' => $sid, ];

        $this->uri = '/Assistants/' . \rawurlencode($assistantSid) . '/FieldTypes/' . \rawurlencode($fieldTypeSid) . '/FieldValues/' . \rawurlencode($sid) . '';
    }

    /**
     * Fetch the FieldValueInstance
     *
     * @return FieldValueInstance Fetched FieldValueInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): FieldValueInstance {
        $payload = $this->version->fetch('GET', $this->uri);

        return new FieldValueInstance(
            $this->version,
            $payload,
            $this->solution['assistantSid'],
            $this->solution['fieldTypeSid'],
            $this->solution['sid']
        );
    }

    /**
     * Delete the FieldValueInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool {
        return $this->version->delete('DELETE', $this->uri);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Preview.Understand.FieldValueContext ' . \implode(' ', $context) . ']';
    }
}