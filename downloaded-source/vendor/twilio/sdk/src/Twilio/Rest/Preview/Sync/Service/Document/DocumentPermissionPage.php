<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Preview\Sync\Service\Document;

use <PERSON>wi<PERSON>\Http\Response;
use <PERSON><PERSON><PERSON>\Page;
use <PERSON>wilio\Version;

/**
 * PLEASE NOTE that this class contains preview products that are subject to change. Use them with caution. If you currently do not have developer preview access, <NAME_EMAIL>.
 */
class DocumentPermissionPage extends Page {
    /**
     * @param Version $version Version that contains the resource
     * @param Response $response Response from the API
     * @param array $solution The context solution
     */
    public function __construct(Version $version, Response $response, array $solution) {
        parent::__construct($version, $response);

        // Path Solution
        $this->solution = $solution;
    }

    /**
     * @param array $payload Payload response from the API
     * @return DocumentPermissionInstance \Twilio\Rest\Preview\Sync\Service\Document\DocumentPermissionInstance
     */
    public function buildInstance(array $payload): DocumentPermissionInstance {
        return new DocumentPermissionInstance(
            $this->version,
            $payload,
            $this->solution['serviceSid'],
            $this->solution['documentSid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Preview.Sync.DocumentPermissionPage]';
    }
}