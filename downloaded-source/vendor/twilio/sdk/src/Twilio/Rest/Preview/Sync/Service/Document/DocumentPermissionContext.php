<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Preview\Sync\Service\Document;

use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\InstanceContext;
use <PERSON><PERSON><PERSON>\Serialize;
use Twilio\Values;
use Twilio\Version;

/**
 * PLEASE NOTE that this class contains preview products that are subject to change. Use them with caution. If you currently do not have developer preview access, <NAME_EMAIL>.
 */
class DocumentPermissionContext extends InstanceContext {
    /**
     * Initialize the DocumentPermissionContext
     *
     * @param Version $version Version that contains the resource
     * @param string $serviceSid The service_sid
     * @param string $documentSid Sync Document SID or unique name.
     * @param string $identity Identity of the user to whom the Sync Document
     *                         Permission applies.
     */
    public function __construct(Version $version, $serviceSid, $documentSid, $identity) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
            'serviceSid' => $serviceSid,
            'documentSid' => $documentSid,
            'identity' => $identity,
        ];

        $this->uri = '/Services/' . \rawurlencode($serviceSid) . '/Documents/' . \rawurlencode($documentSid) . '/Permissions/' . \rawurlencode($identity) . '';
    }

    /**
     * Fetch the DocumentPermissionInstance
     *
     * @return DocumentPermissionInstance Fetched DocumentPermissionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): DocumentPermissionInstance {
        $payload = $this->version->fetch('GET', $this->uri);

        return new DocumentPermissionInstance(
            $this->version,
            $payload,
            $this->solution['serviceSid'],
            $this->solution['documentSid'],
            $this->solution['identity']
        );
    }

    /**
     * Delete the DocumentPermissionInstance
     *
     * @return bool True if delete succeeds, false otherwise
     * @throws TwilioException When an HTTP error occurs.
     */
    public function delete(): bool {
        return $this->version->delete('DELETE', $this->uri);
    }

    /**
     * Update the DocumentPermissionInstance
     *
     * @param bool $read Read access.
     * @param bool $write Write access.
     * @param bool $manage Manage access.
     * @return DocumentPermissionInstance Updated DocumentPermissionInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(bool $read, bool $write, bool $manage): DocumentPermissionInstance {
        $data = Values::of([
            'Read' => Serialize::booleanToString($read),
            'Write' => Serialize::booleanToString($write),
            'Manage' => Serialize::booleanToString($manage),
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new DocumentPermissionInstance(
            $this->version,
            $payload,
            $this->solution['serviceSid'],
            $this->solution['documentSid'],
            $this->solution['identity']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Preview.Sync.DocumentPermissionContext ' . \implode(' ', $context) . ']';
    }
}