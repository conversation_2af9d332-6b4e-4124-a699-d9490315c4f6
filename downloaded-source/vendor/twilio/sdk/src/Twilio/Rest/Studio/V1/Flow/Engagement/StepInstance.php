<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Studio\V1\Flow\Engagement;

use <PERSON><PERSON><PERSON>\Deserialize;
use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use T<PERSON><PERSON>\InstanceResource;
use Twilio\Rest\Studio\V1\Flow\Engagement\Step\StepContextList;
use Twilio\Values;
use Twilio\Version;

/**
 * @property string $sid
 * @property string $accountSid
 * @property string $flowSid
 * @property string $engagementSid
 * @property string $name
 * @property array $context
 * @property string $transitionedFrom
 * @property string $transitionedTo
 * @property \DateTime $dateCreated
 * @property \DateTime $dateUpdated
 * @property string $url
 * @property array $links
 */
class StepInstance extends InstanceResource {
    protected $_stepContext;

    /**
     * Initialize the StepInstance
     *
     * @param Version $version Version that contains the resource
     * @param mixed[] $payload The response payload
     * @param string $flowSid The SID of the Flow
     * @param string $engagementSid The SID of the Engagement
     * @param string $sid The SID that identifies the resource to fetch
     */
    public function __construct(Version $version, array $payload, string $flowSid, string $engagementSid, string $sid = null) {
        parent::__construct($version);

        // Marshaled Properties
        $this->properties = [
            'sid' => Values::array_get($payload, 'sid'),
            'accountSid' => Values::array_get($payload, 'account_sid'),
            'flowSid' => Values::array_get($payload, 'flow_sid'),
            'engagementSid' => Values::array_get($payload, 'engagement_sid'),
            'name' => Values::array_get($payload, 'name'),
            'context' => Values::array_get($payload, 'context'),
            'transitionedFrom' => Values::array_get($payload, 'transitioned_from'),
            'transitionedTo' => Values::array_get($payload, 'transitioned_to'),
            'dateCreated' => Deserialize::dateTime(Values::array_get($payload, 'date_created')),
            'dateUpdated' => Deserialize::dateTime(Values::array_get($payload, 'date_updated')),
            'url' => Values::array_get($payload, 'url'),
            'links' => Values::array_get($payload, 'links'),
        ];

        $this->solution = [
            'flowSid' => $flowSid,
            'engagementSid' => $engagementSid,
            'sid' => $sid ?: $this->properties['sid'],
        ];
    }

    /**
     * Generate an instance context for the instance, the context is capable of
     * performing various actions.  All instance actions are proxied to the context
     *
     * @return StepContext Context for this StepInstance
     */
    protected function proxy(): StepContext {
        if (!$this->context) {
            $this->context = new StepContext(
                $this->version,
                $this->solution['flowSid'],
                $this->solution['engagementSid'],
                $this->solution['sid']
            );
        }

        return $this->context;
    }

    /**
     * Fetch the StepInstance
     *
     * @return StepInstance Fetched StepInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function fetch(): StepInstance {
        return $this->proxy()->fetch();
    }

    /**
     * Access the stepContext
     */
    protected function getStepContext(): StepContextList {
        return $this->proxy()->stepContext;
    }

    /**
     * Magic getter to access properties
     *
     * @param string $name Property to access
     * @return mixed The requested property
     * @throws TwilioException For unknown properties
     */
    public function __get(string $name) {
        if (\array_key_exists($name, $this->properties)) {
            return $this->properties[$name];
        }

        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown property: ' . $name);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $context = [];
        foreach ($this->solution as $key => $value) {
            $context[] = "$key=$value";
        }
        return '[Twilio.Studio.V1.StepInstance ' . \implode(' ', $context) . ']';
    }
}