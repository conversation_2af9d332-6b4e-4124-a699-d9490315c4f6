<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Studio\V2;

use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\ListResource;
use <PERSON><PERSON><PERSON>\Options;
use <PERSON><PERSON><PERSON>\Serialize;
use Twilio\Values;
use Twilio\Version;

class FlowValidateList extends ListResource {
    /**
     * Construct the FlowValidateList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(Version $version) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [];

        $this->uri = '/Flows/Validate';
    }

    /**
     * Update the FlowValidateInstance
     *
     * @param string $friendlyName The string that you assigned to describe the Flow
     * @param string $status The status of the Flow
     * @param array $definition JSON representation of flow definition
     * @param array|Options $options Optional Arguments
     * @return FlowValidateInstance Updated FlowValidateInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function update(string $friendlyName, string $status, array $definition, array $options = []): FlowValidateInstance {
        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' => $friendlyName,
            'Status' => $status,
            'Definition' => Serialize::jsonObject($definition),
            'CommitMessage' => $options['commitMessage'],
        ]);

        $payload = $this->version->update('POST', $this->uri, [], $data);

        return new FlowValidateInstance($this->version, $payload);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Studio.V2.FlowValidateList]';
    }
}