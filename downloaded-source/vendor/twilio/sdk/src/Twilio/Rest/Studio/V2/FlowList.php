<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Studio\V2;

use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use <PERSON><PERSON>lio\ListResource;
use <PERSON><PERSON><PERSON>\Options;
use T<PERSON><PERSON>\Serialize;
use Twilio\Stream;
use Twilio\Values;
use Twilio\Version;

class FlowList extends ListResource {
    /**
     * Construct the FlowList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(Version $version) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [];

        $this->uri = '/Flows';
    }

    /**
     * Create the FlowInstance
     *
     * @param string $friendlyName The string that you assigned to describe the Flow
     * @param string $status The status of the Flow
     * @param array $definition JSON representation of flow definition
     * @param array|Options $options Optional Arguments
     * @return FlowInstance Created FlowInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(string $friendlyName, string $status, array $definition, array $options = []): FlowInstance {
        $options = new Values($options);

        $data = Values::of([
            'FriendlyName' => $friendlyName,
            'Status' => $status,
            'Definition' => Serialize::jsonObject($definition),
            'CommitMessage' => $options['commitMessage'],
        ]);

        $payload = $this->version->create('POST', $this->uri, [], $data);

        return new FlowInstance($this->version, $payload);
    }

    /**
     * Streams FlowInstance records from the API as a generator stream.
     * This operation lazily loads records as efficiently as possible until the
     * limit
     * is reached.
     * The results are returned as a generator, so this operation is memory
     * efficient.
     *
     * @param int $limit Upper limit for the number of records to return. stream()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, stream()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return Stream stream of results
     */
    public function stream(int $limit = null, $pageSize = null): Stream {
        $limits = $this->version->readLimits($limit, $pageSize);

        $page = $this->page($limits['pageSize']);

        return $this->version->stream($page, $limits['limit'], $limits['pageLimit']);
    }

    /**
     * Reads FlowInstance records from the API as a list.
     * Unlike stream(), this operation is eager and will load `limit` records into
     * memory before returning.
     *
     * @param int $limit Upper limit for the number of records to return. read()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, read()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return FlowInstance[] Array of results
     */
    public function read(int $limit = null, $pageSize = null): array {
        return \iterator_to_array($this->stream($limit, $pageSize), false);
    }

    /**
     * Retrieve a single page of FlowInstance records from the API.
     * Request is executed immediately
     *
     * @param mixed $pageSize Number of records to return, defaults to 50
     * @param string $pageToken PageToken provided by the API
     * @param mixed $pageNumber Page Number, this value is simply for client state
     * @return FlowPage Page of FlowInstance
     */
    public function page($pageSize = Values::NONE, string $pageToken = Values::NONE, $pageNumber = Values::NONE): FlowPage {
        $params = Values::of(['PageToken' => $pageToken, 'Page' => $pageNumber, 'PageSize' => $pageSize, ]);

        $response = $this->version->page('GET', $this->uri, $params);

        return new FlowPage($this->version, $response, $this->solution);
    }

    /**
     * Retrieve a specific page of FlowInstance records from the API.
     * Request is executed immediately
     *
     * @param string $targetUrl API-generated URL for the requested results page
     * @return FlowPage Page of FlowInstance
     */
    public function getPage(string $targetUrl): FlowPage {
        $response = $this->version->getDomain()->getClient()->request(
            'GET',
            $targetUrl
        );

        return new FlowPage($this->version, $response, $this->solution);
    }

    /**
     * Constructs a FlowContext
     *
     * @param string $sid The SID that identifies the resource to fetch
     */
    public function getContext(string $sid): FlowContext {
        return new FlowContext($this->version, $sid);
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Studio.V2.FlowList]';
    }
}