<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Studio\V2\Flow;

use <PERSON>wi<PERSON>\Options;
use Twilio\Values;

abstract class ExecutionOptions {
    /**
     * @param \DateTime $dateCreatedFrom Only show Executions that started on or
     *                                   after this ISO 8601 date-time
     * @param \DateTime $dateCreatedTo Only show Executions that started before
     *                                 this ISO 8601 date-time
     * @return ReadExecutionOptions Options builder
     */
    public static function read(\DateTime $dateCreatedFrom = Values::NONE, \DateTime $dateCreatedTo = Values::NONE): ReadExecutionOptions {
        return new ReadExecutionOptions($dateCreatedFrom, $dateCreatedTo);
    }

    /**
     * @param array $parameters JSON data that will be added to the Flow's context
     * @return CreateExecutionOptions Options builder
     */
    public static function create(array $parameters = Values::ARRAY_NONE): CreateExecutionOptions {
        return new CreateExecutionOptions($parameters);
    }
}

class ReadExecutionOptions extends Options {
    /**
     * @param \DateTime $dateCreatedFrom Only show Executions that started on or
     *                                   after this ISO 8601 date-time
     * @param \DateTime $dateCreatedTo Only show Executions that started before
     *                                 this ISO 8601 date-time
     */
    public function __construct(\DateTime $dateCreatedFrom = Values::NONE, \DateTime $dateCreatedTo = Values::NONE) {
        $this->options['dateCreatedFrom'] = $dateCreatedFrom;
        $this->options['dateCreatedTo'] = $dateCreatedTo;
    }

    /**
     * Only show Execution resources starting on or after this [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time, given as `YYYY-MM-DDThh:mm:ss-hh:mm`.
     *
     * @param \DateTime $dateCreatedFrom Only show Executions that started on or
     *                                   after this ISO 8601 date-time
     * @return $this Fluent Builder
     */
    public function setDateCreatedFrom(\DateTime $dateCreatedFrom): self {
        $this->options['dateCreatedFrom'] = $dateCreatedFrom;
        return $this;
    }

    /**
     * Only show Execution resources starting before this [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601) date-time, given as `YYYY-MM-DDThh:mm:ss-hh:mm`.
     *
     * @param \DateTime $dateCreatedTo Only show Executions that started before
     *                                 this ISO 8601 date-time
     * @return $this Fluent Builder
     */
    public function setDateCreatedTo(\DateTime $dateCreatedTo): self {
        $this->options['dateCreatedTo'] = $dateCreatedTo;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Studio.V2.ReadExecutionOptions ' . $options . ']';
    }
}

class CreateExecutionOptions extends Options {
    /**
     * @param array $parameters JSON data that will be added to the Flow's context
     */
    public function __construct(array $parameters = Values::ARRAY_NONE) {
        $this->options['parameters'] = $parameters;
    }

    /**
     * JSON data that will be added to the Flow's context and that can be accessed as variables inside your Flow. For example, if you pass in `Parameters={"name":"Zeke"}`, a widget in your Flow can reference the variable `{{flow.data.name}}`, which returns "Zeke". Note: the JSON value must explicitly be passed as a string, not as a hash object. Depending on your particular HTTP library, you may need to add quotes or URL encode the JSON string.
     *
     * @param array $parameters JSON data that will be added to the Flow's context
     * @return $this Fluent Builder
     */
    public function setParameters(array $parameters): self {
        $this->options['parameters'] = $parameters;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Studio.V2.CreateExecutionOptions ' . $options . ']';
    }
}