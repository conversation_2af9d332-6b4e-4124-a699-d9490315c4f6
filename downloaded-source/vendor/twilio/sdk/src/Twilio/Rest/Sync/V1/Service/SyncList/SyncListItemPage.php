<?php

/**
 * This code was generated by
 * \ / _    _  _|   _  _
 * | (_)\/(_)(_|\/| |(/_  v1.0.0
 * /       /
 */

namespace Twilio\Rest\Sync\V1\Service\SyncList;

use <PERSON><PERSON><PERSON>\Http\Response;
use <PERSON><PERSON><PERSON>\Page;
use <PERSON><PERSON><PERSON>\Version;

class SyncListItemPage extends Page {
    /**
     * @param Version $version Version that contains the resource
     * @param Response $response Response from the API
     * @param array $solution The context solution
     */
    public function __construct(Version $version, Response $response, array $solution) {
        parent::__construct($version, $response);

        // Path Solution
        $this->solution = $solution;
    }

    /**
     * @param array $payload Payload response from the API
     * @return SyncListItemInstance \Twilio\Rest\Sync\V1\Service\SyncList\SyncListItemInstance
     */
    public function buildInstance(array $payload): SyncListItemInstance {
        return new SyncListItemInstance(
            $this->version,
            $payload,
            $this->solution['serviceSid'],
            $this->solution['listSid']
        );
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string {
        return '[Twilio.Sync.V1.SyncListItemPage]';
    }
}