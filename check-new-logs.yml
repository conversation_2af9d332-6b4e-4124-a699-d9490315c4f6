---
- name: Check Application Logs After Fix
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite

  tasks:
    - name: Check latest application logs
      shell: tail -30 {{ app_path }}/storage/logs/laravel.log
      register: latest_logs
      ignore_errors: yes

    - name: Check PHP-FPM error logs
      shell: tail -20 /var/log/php8.2-fpm.log
      register: php_fpm_logs
      ignore_errors: yes

    - name: Check Nginx error logs
      shell: tail -20 /var/log/nginx/error.log
      register: nginx_logs
      ignore_errors: yes

    - name: Test direct PHP file access
      shell: |
        cd {{ app_path }}
        echo "<?php phpinfo(); ?>" > test.php
        curl -s http://localhost:8601/test.php | head -10
        rm test.php
      register: php_test
      ignore_errors: yes

    - name: Check database connection
      shell: |
        cd {{ app_path }}
        sudo -u worksuite php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connected successfully';"
      register: db_test
      ignore_errors: yes

    - name: Check .env file configuration
      shell: |
        cd {{ app_path }}
        grep -E "^(APP_|DB_)" .env
      register: env_check
      ignore_errors: yes

    - name: Display comprehensive log analysis
      debug:
        msg: |
          🔍 APPLICATION LOG ANALYSIS
          ===========================
          
          📋 Latest Application Logs:
          {{ latest_logs.stdout | default('No logs found') }}
          
          🐘 PHP-FPM Logs:
          {{ php_fpm_logs.stdout | default('No errors') }}
          
          🌐 Nginx Logs:
          {{ nginx_logs.stdout | default('No errors') }}
          
          🧪 PHP Test:
          {{ php_test.stdout | default('Failed') }}
          
          🗄️ Database Test:
          {{ db_test.stdout | default('Failed') }}
          
          ⚙️ Environment Configuration:
          {{ env_check.stdout | default('No config found') }}
          
          💡 RECOMMENDATIONS:
          {% if 'Database connected successfully' in db_test.stdout %}
          ✅ Database connection is working
          {% else %}
          ❌ Database connection issues detected
          {% endif %}
          
          {% if 'PHP' in php_test.stdout %}
          ✅ PHP is working correctly
          {% else %}
          ❌ PHP execution issues detected
          {% endif %}
