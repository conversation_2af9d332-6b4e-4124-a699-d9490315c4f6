---
- name: Complete WorkSuite SAAS Deployment
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite
    web_user: worksuite

  tasks:
    - name: Generate application key
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan key:generate --force
      ignore_errors: yes

    - name: Run database migrations
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan migrate --force
      ignore_errors: yes

    - name: Create symbolic link for storage
      file:
        src: "{{ app_path }}/storage/app/public"
        dest: "{{ app_path }}/public/storage"
        state: link
        owner: "{{ web_user }}"
        group: www-data
      ignore_errors: yes

    - name: Clear Laravel caches
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan config:clear
        sudo -u {{ web_user }} php artisan cache:clear
        sudo -u {{ web_user }} php artisan route:clear
        sudo -u {{ web_user }} php artisan view:clear
      ignore_errors: yes

    - name: Start services
      systemd:
        name: "{{ item }}"
        state: started
        enabled: yes
      loop:
        - php8.2-fpm
        - nginx

    - name: Wait for services to be ready
      wait_for:
        port: "{{ item }}"
        host: localhost
        timeout: 30
      loop:
        - 8601
        - 8643

    - name: Test application
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 30
      register: app_test
      ignore_errors: yes

    - name: Display deployment results
      debug:
        msg: |
          🎉 Deployment Complete!
          
          ✅ Source code deployed successfully
          ✅ Permissions set correctly
          ✅ Services started
          
          Application Status: {{ 'SUCCESS' if app_test.status == 200 or app_test.status == 302 else 'NEEDS_SETUP' }}
          
          Access URLs:
          - HTTP: http://{{ ansible_host }}:8601
          - HTTPS: https://{{ ansible_host }}:8643
          - Public: https://erp.iti.id.vn
          
          Next Steps:
          1. Visit the application URL
          2. Complete the initial setup if required
          3. Configure your application settings
