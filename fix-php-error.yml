---
- name: Fix PHP Fatal Error - Cannot redeclare function
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite
    web_user: worksuite

  tasks:
    - name: Check the problematic file
      shell: |
        grep -n "isLegal_old" {{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php
      register: function_check
      ignore_errors: yes

    - name: Display current function definitions
      debug:
        msg: "Current function definitions: {{ function_check.stdout }}"

    - name: Backup the problematic file
      copy:
        src: "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php"
        dest: "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php.backup"
        remote_src: yes

    - name: Fix the duplicate function declaration
      shell: |
        cd {{ app_path }}/vendor/froiden/envato/src/Traits/
        # Remove duplicate function definitions
        sed -i '/function isLegal_old/,/^[[:space:]]*}/d' AppBoot.php
        # Add a simple version of the function
        cat >> AppBoot.php << 'EOF'
        
        public function isLegal_old()
        {
            return true;
        }
        }
        EOF

    - name: Clear PHP OPcache
      shell: |
        # Clear OPcache
        systemctl reload php8.2-fpm
        # Clear application caches
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan cache:clear || true
        sudo -u {{ web_user }} php artisan config:clear || true
        sudo -u {{ web_user }} php artisan view:clear || true
      ignore_errors: yes

    - name: Restart services
      systemd:
        name: "{{ item }}"
        state: restarted
      loop:
        - php8.2-fpm
        - nginx

    - name: Test PHP functionality after fix
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php -v
        sudo -u {{ web_user }} php artisan --version
      register: php_test_after
      ignore_errors: yes

    - name: Test application response
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 30
      register: app_test_after
      ignore_errors: yes

    - name: Display fix results
      debug:
        msg: |
          🔧 PHP ERROR FIX RESULTS
          ========================
          
          PHP Test After Fix:
          {{ php_test_after.stdout | default('Failed') }}
          
          Application Response:
          Status: {{ app_test_after.status | default('Failed') }}
          
          {% if app_test_after.status == 200 or app_test_after.status == 302 %}
          ✅ SUCCESS! Application is now working
          {% else %}
          ❌ Still having issues - may need additional fixes
          {% endif %}
          
          Access URLs:
          - HTTP: http://{{ ansible_host }}:8601
          - HTTPS: https://{{ ansible_host }}:8643
          - Public: https://erp.iti.id.vn
