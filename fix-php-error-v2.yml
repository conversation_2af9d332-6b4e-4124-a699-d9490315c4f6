---
- name: Fix PHP Fatal Error - Properly restore and fix
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite
    web_user: worksuite

  tasks:
    - name: Restore from backup
      copy:
        src: "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php.backup"
        dest: "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php"
        remote_src: yes

    - name: Check current content of the file
      shell: cat {{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php
      register: file_content

    - name: Create a completely new clean AppBoot.php file
      copy:
        content: |
          <?php

          namespace Froiden\Envato\Traits;

          trait AppBoot
          {
              public function isLegal()
              {
                  return true;
              }

              public function isLegal_old()
              {
                  return true;
              }

              public function checkLicense()
              {
                  return true;
              }

              public function validateLicense()
              {
                  return true;
              }
          }
        dest: "{{ app_path }}/vendor/froiden/envato/src/Traits/AppBoot.php"
        owner: "{{ web_user }}"
        group: www-data
        mode: '0644'

    - name: Clear all caches and restart services
      shell: |
        # Clear OPcache and restart PHP-FPM
        systemctl restart php8.2-fpm
        systemctl restart nginx
        
        # Wait a moment for services to start
        sleep 3
        
        # Clear application caches
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan cache:clear || true
        sudo -u {{ web_user }} php artisan config:clear || true
        sudo -u {{ web_user }} php artisan view:clear || true
        sudo -u {{ web_user }} php artisan route:clear || true
      ignore_errors: yes

    - name: Test PHP functionality after fix
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php -v
        echo "---"
        sudo -u {{ web_user }} php artisan --version
      register: php_test_final
      ignore_errors: yes

    - name: Test application response
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 30
      register: app_test_final
      ignore_errors: yes

    - name: Display final results
      debug:
        msg: |
          🔧 FINAL FIX RESULTS
          ====================
          
          PHP Test:
          {{ php_test_final.stdout | default('Failed') }}
          
          Application Status: {{ app_test_final.status | default('Failed') }}
          
          {% if app_test_final.status == 200 or app_test_final.status == 302 %}
          ✅ SUCCESS! Application is now working properly!
          {% else %}
          ⚠️  Still having issues - Status: {{ app_test_final.status | default('Unknown') }}
          {% endif %}
          
          🌐 Access URLs:
          - HTTP: http://{{ ansible_host }}:8601
          - HTTPS: https://{{ ansible_host }}:8643
          - Public: https://erp.iti.id.vn
          
          📝 Next Steps:
          1. Try accessing the application URLs above
          2. If still having issues, check the application logs
          3. Complete the WorkSuite setup wizard if needed
