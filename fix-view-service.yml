---
- name: Fix Laravel View Service Provider Issue
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite
    web_user: worksuite

  tasks:
    - name: Clear all Laravel caches completely
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan cache:clear || true
        sudo -u {{ web_user }} php artisan config:clear || true
        sudo -u {{ web_user }} php artisan view:clear || true
        sudo -u {{ web_user }} php artisan route:clear || true
        sudo -u {{ web_user }} php artisan event:clear || true
        
        # Clear compiled files
        rm -rf {{ app_path }}/bootstrap/cache/*.php || true
        rm -rf {{ app_path }}/storage/framework/cache/data/* || true
        rm -rf {{ app_path }}/storage/framework/sessions/* || true
        rm -rf {{ app_path }}/storage/framework/views/* || true
        
        # Regenerate autoload
        sudo -u {{ web_user }} composer dump-autoload --optimize || true
      ignore_errors: yes

    - name: Regenerate application key
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan key:generate --force
      ignore_errors: yes

    - name: Check and fix config/app.php providers
      shell: |
        cd {{ app_path }}
        # Check if providers are properly configured
        grep -n "ViewServiceProvider" config/app.php || echo "ViewServiceProvider not found"
        grep -n "providers" config/app.php | head -5
      register: providers_check
      ignore_errors: yes

    - name: Run Laravel optimize commands
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan optimize:clear || true
        sudo -u {{ web_user }} php artisan config:cache || true
        sudo -u {{ web_user }} php artisan route:cache || true
        sudo -u {{ web_user }} php artisan view:cache || true
      ignore_errors: yes

    - name: Set proper permissions again
      shell: |
        chown -R {{ web_user }}:www-data {{ app_path }}
        chmod -R 755 {{ app_path }}
        chmod -R 775 {{ app_path }}/storage
        chmod -R 775 {{ app_path }}/bootstrap/cache
      ignore_errors: yes

    - name: Restart services
      systemd:
        name: "{{ item }}"
        state: restarted
      loop:
        - php8.2-fpm
        - nginx

    - name: Test application after fixes
      uri:
        url: "https://localhost:8643"
        method: GET
        validate_certs: no
        timeout: 30
      register: final_test
      ignore_errors: yes

    - name: Check latest logs after fix
      shell: tail -10 {{ app_path }}/storage/logs/laravel.log
      register: final_logs
      ignore_errors: yes

    - name: Display final status
      debug:
        msg: |
          🔧 LARAVEL VIEW SERVICE FIX RESULTS
          ===================================
          
          📋 Providers Check:
          {{ providers_check.stdout | default('No output') }}
          
          🌐 Application Test:
          Status: {{ final_test.status | default('Failed') }}
          
          {% if final_test.status == 200 or final_test.status == 302 %}
          🎉 SUCCESS! Application is now working!
          {% elif final_test.status == 500 %}
          ⚠️  Still getting 500 error - check logs below
          {% else %}
          ❌ Application test failed - Status: {{ final_test.status | default('Unknown') }}
          {% endif %}
          
          📋 Latest Logs:
          {{ final_logs.stdout | default('No logs') }}
          
          🌐 Access URLs:
          - HTTP: http://{{ ansible_host }}:8601
          - HTTPS: https://{{ ansible_host }}:8643
          - Public: https://erp.iti.id.vn
          
          💡 Next Steps:
          {% if final_test.status == 200 or final_test.status == 302 %}
          ✅ Application is working! You can now access it via the URLs above.
          {% else %}
          1. Check the latest logs above for any remaining errors
          2. Try accessing the application URLs manually
          3. If still having issues, we may need to check specific Laravel configuration
          {% endif %}
