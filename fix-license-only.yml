---
- name: Fix License Issues Only
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite
    web_user: worksuite

  tasks:
    - name: Apply license bypass modifications
      include_tasks: tasks/disable-license-check.yml

    - name: Generate application key
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan key:generate --force
      ignore_errors: yes

    - name: Run database migrations
      shell: |
        cd {{ app_path }}
        sudo -u {{ web_user }} php artisan migrate --force
      ignore_errors: yes

    - name: Create symbolic link for storage
      file:
        src: "{{ app_path }}/storage/app/public"
        dest: "{{ app_path }}/public/storage"
        state: link
        owner: "{{ web_user }}"
        group: www-data
      ignore_errors: yes

    - name: Start services
      systemd:
        name: "{{ item }}"
        state: started
        enabled: yes
      loop:
        - php8.2-fpm
        - nginx

    - name: Display completion message
      debug:
        msg: |
          🎉 License Fix Complete!
          
          ✅ License checks disabled
          ✅ Application key generated
          ✅ Database migrations run
          ✅ Services started
          
          Access URLs:
          - Public: https://erp.iti.id.vn
          - Direct: https://{{ ansible_host }}:8643
          
          Login Credentials:
          - Email: <EMAIL>
          - Password: password123
