---
- name: Check All Services Status
  hosts: worksuite-production
  become: yes
  vars:
    app_path: /var/www/worksuite

  tasks:
    - name: Check Nginx status
      systemd:
        name: nginx
      register: nginx_status

    - name: Check PHP-FPM status
      systemd:
        name: php8.2-fpm
      register: php_status

    - name: Check Cloudflared status
      systemd:
        name: cloudflared
      register: cloudflared_status
      ignore_errors: yes

    - name: Check if ports are listening
      shell: netstat -tlnp | grep -E ':(80|443|8601|8643|22)'
      register: port_status
      ignore_errors: yes

    - name: Check Nginx configuration
      shell: nginx -t
      register: nginx_config
      ignore_errors: yes

    - name: Check PHP-FPM configuration
      shell: php-fpm8.2 -t
      register: php_config
      ignore_errors: yes

    - name: Check application logs
      shell: tail -20 /var/www/worksuite/storage/logs/laravel.log
      register: app_logs
      ignore_errors: yes

    - name: Check Nginx error logs
      shell: tail -20 /var/log/nginx/error.log
      register: nginx_logs
      ignore_errors: yes

    - name: Check PHP-FPM logs
      shell: tail -20 /var/log/php8.2-fpm.log
      register: php_logs
      ignore_errors: yes

    - name: Check disk space
      shell: df -h /var/www
      register: disk_space

    - name: Check application permissions
      shell: |
        ls -la /var/www/worksuite/
        ls -la /var/www/worksuite/storage/
        ls -la /var/www/worksuite/bootstrap/cache/
      register: permissions_check

    - name: Test PHP functionality
      shell: |
        cd /var/www/worksuite
        sudo -u worksuite php -v
        sudo -u worksuite php artisan --version
      register: php_test
      ignore_errors: yes

    - name: Display comprehensive status report
      debug:
        msg: |
          🔍 SERVICE STATUS REPORT
          ========================
          
          📊 SERVICES STATUS:
          - Nginx: {{ nginx_status.status.ActiveState }}
          - PHP-FPM: {{ php_status.status.ActiveState }}
          - Cloudflared: {{ cloudflared_status.status.ActiveState | default('not found') }}
          
          🌐 LISTENING PORTS:
          {{ port_status.stdout }}
          
          ⚙️ CONFIGURATION TESTS:
          Nginx Config: {{ 'OK' if nginx_config.rc == 0 else 'ERROR' }}
          PHP-FPM Config: {{ 'OK' if php_config.rc == 0 else 'ERROR' }}
          
          💾 DISK SPACE:
          {{ disk_space.stdout }}
          
          🐛 RECENT LOGS:
          
          Application Logs:
          {{ app_logs.stdout | default('No logs found') }}
          
          Nginx Error Logs:
          {{ nginx_logs.stdout | default('No errors') }}
          
          PHP-FPM Logs:
          {{ php_logs.stdout | default('No errors') }}
          
          🔧 PHP TEST:
          {{ php_test.stdout | default('PHP test failed') }}
          
          📁 PERMISSIONS:
          {{ permissions_check.stdout }}
