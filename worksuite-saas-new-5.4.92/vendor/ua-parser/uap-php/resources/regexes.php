<?php
return [
    'user_agent_parsers' => [
        ['regex' => '@(GeoEvent Server) (\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)@'],
        ['regex' => '@(ArcGIS Pro)(?: (\\d+)\\.(\\d+)\\.([^ ]+)|)@'],
        [
            'regex' => '@ArcGIS Client Using WinInet@',
            'family_replacement' => 'ArcMap',
        ],
        [
            'regex' => '@(OperationsDashboard)-(?:Windows)-(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Operations Dashboard for ArcGIS',
        ],
        [
            'regex' => '@(arcgisearth)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'ArcGIS Earth',
        ],
        [
            'regex' => '@com.esri.(earth).phone/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'ArcGIS Earth',
        ],
        [
            'regex' => '@(arcgis-explorer)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Explorer for ArcGIS',
        ],
        [
            'regex' => '@arcgis-(collector|aurora)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Collector for ArcGIS',
        ],
        [
            'regex' => '@(arcgis-workforce)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Workforce for ArcGIS',
        ],
        [
            'regex' => '@(Collector|Explorer|Workforce)-(?:Android|iOS)-(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => '$1 for ArcGIS',
        ],
        [
            'regex' => '@(Explorer|Collector)/(\\d+) CFNetwork@',
            'family_replacement' => '$1 for ArcGIS',
        ],
        [
            'regex' => '@ArcGISRuntime-(Android|iOS|NET|Qt)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'ArcGIS Runtime SDK for $1',
        ],
        [
            'regex' => '@ArcGIS\\.?(iOS|Android|NET|Qt)(?:-|\\.)(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'ArcGIS Runtime SDK for $1',
        ],
        [
            'regex' => '@ArcGIS\\.Runtime\\.(Qt)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'ArcGIS Runtime SDK for $1',
        ],
        ['regex' => '@^(Luminary)[Stage]+/(\\d+) CFNetwork@'],
        ['regex' => '@(ESPN)[%20| ]+Radio/(\\d+)\\.(\\d+)\\.(\\d+) CFNetwork@'],
        [
            'regex' => '@(Antenna)/(\\d+) CFNetwork@',
            'family_replacement' => 'AntennaPod',
        ],
        ['regex' => '@(TopPodcasts)Pro/(\\d+) CFNetwork@'],
        ['regex' => '@(MusicDownloader)Lite/(\\d+)\\.(\\d+)\\.(\\d+) CFNetwork@'],
        ['regex' => '@^(.*)-iPad\\/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|) CFNetwork@'],
        ['regex' => '@^(.*)-iPhone/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|) CFNetwork@'],
        ['regex' => '@^(.*)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|) CFNetwork@'],
        ['regex' => '@^(Luminary)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@(espn\\.go)@',
            'family_replacement' => 'ESPN',
        ],
        [
            'regex' => '@(espnradio\\.com)@',
            'family_replacement' => 'ESPN',
        ],
        [
            'regex' => '@ESPN APP$@',
            'family_replacement' => 'ESPN',
        ],
        [
            'regex' => '@(audioboom\\.com)@',
            'family_replacement' => 'AudioBoom',
        ],
        ['regex' => '@ (Rivo) RHYTHM@'],
        [
            'regex' => '@(CFNetwork)(?:/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)@',
            'family_replacement' => 'CFNetwork',
        ],
        [
            'regex' => '@(Pingdom\\.com_bot_version_)(\\d+)\\.(\\d+)@',
            'family_replacement' => 'PingdomBot',
        ],
        [
            'regex' => '@(PingdomTMS)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'PingdomBot',
        ],
        [
            'regex' => '@ (PTST)/(\\d+)(?:\\.(\\d+)|)$@',
            'family_replacement' => 'WebPageTest.org bot',
        ],
        ['regex' => '@X11; (Datanyze); Linux@'],
        [
            'regex' => '@(NewRelicPinger)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'NewRelicPingerBot',
        ],
        [
            'regex' => '@(Tableau)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Tableau',
        ],
        [
            'regex' => '@AppleWebKit/\\d+\\.\\d+.* Safari.* (CreativeCloud)/(\\d+)\\.(\\d+).(\\d+)@',
            'family_replacement' => 'Adobe CreativeCloud',
        ],
        ['regex' => '@(Salesforce)(?:.)\\/(\\d+)\\.(\\d?)@'],
        [
            'regex' => '@(\\(StatusCake\\))@',
            'family_replacement' => 'StatusCakeBot',
        ],
        [
            'regex' => '@(facebookexternalhit)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'FacebookBot',
        ],
        [
            'regex' => '@Google.*/\\+/web/snippet@',
            'family_replacement' => 'GooglePlusBot',
        ],
        [
            'regex' => '@via ggpht\\.com GoogleImageProxy@',
            'family_replacement' => 'GmailImageProxy',
        ],
        [
            'regex' => '@YahooMailProxy; https://help\\.yahoo\\.com/kb/yahoo-mail-proxy-SLN28749\\.html@',
            'family_replacement' => 'YahooMailProxy',
        ],
        [
            'regex' => '@(Twitterbot)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Twitterbot',
        ],
        ['regex' => '@/((?:Ant-|)Nutch|[A-z]+[Bb]ot|[A-z]+[Ss]pider|Axtaris|fetchurl|Isara|ShopSalad|Tailsweep)[ \\-](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        ['regex' => '@\\b(008|Altresium|Argus|BaiduMobaider|BoardReader|DNSGroup|DataparkSearch|EDI|Goodzer|Grub|INGRID|Infohelfer|LinkedInBot|LOOQ|Nutch|OgScrper|PathDefender|Peew|PostPost|Steeler|Twitterbot|VSE|WebCrunch|WebZIP|Y!J-BR[A-Z]|YahooSeeker|envolk|sproose|wminer)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@(MSIE) (\\d+)\\.(\\d+)([a-z]\\d|[a-z]|);.* MSIECrawler@',
            'family_replacement' => 'MSIECrawler',
        ],
        ['regex' => '@(DAVdroid)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        ['regex' => '@(Google-HTTP-Java-Client|Apache-HttpClient|Go-http-client|scalaj-http|http%20client|Python-urllib|HttpMonitor|TLSProber|WinHTTP|JNLP|okhttp|aihttp|reqwest|axios|unirest-(?:java|python|ruby|nodejs|php|net))(?:[ /](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)@'],
        [
            'regex' => '@(Pinterest(?:bot|))/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)[;\\s(]+\\+https://www.pinterest.com/bot.html@',
            'family_replacement' => 'Pinterestbot',
        ],
        ['regex' => '@(CSimpleSpider|Cityreview Robot|CrawlDaddy|CrawlFire|Finderbots|Index crawler|Job Roboter|KiwiStatus Spider|Lijit Crawler|QuerySeekerSpider|ScollSpider|Trends Crawler|USyd-NLP-Spider|SiteCat Webbot|BotName\\/\\$BotVersion|123metaspider-Bot|1470\\.net crawler|50\\.nu|8bo Crawler Bot|Aboundex|Accoona-[A-z]{1,30}-Agent|AdsBot-Google(?:-[a-z]{1,30}|)|altavista|AppEngine-Google|archive.{0,30}\\.org_bot|archiver|Ask Jeeves|[Bb]ai[Dd]u[Ss]pider(?:-[A-Za-z]{1,30})(?:-[A-Za-z]{1,30}|)|bingbot|BingPreview|blitzbot|BlogBridge|Bloglovin|BoardReader Blog Indexer|BoardReader Favicon Fetcher|boitho.com-dc|BotSeer|BUbiNG|\\b\\w{0,30}favicon\\w{0,30}\\b|\\bYeti(?:-[a-z]{1,30}|)|Catchpoint(?: bot|)|[Cc]harlotte|Checklinks|clumboot|Comodo HTTP\\(S\\) Crawler|Comodo-Webinspector-Crawler|ConveraCrawler|CRAWL-E|CrawlConvera|Daumoa(?:-feedfetcher|)|Feed Seeker Bot|Feedbin|findlinks|Flamingo_SearchEngine|FollowSite Bot|furlbot|Genieo|gigabot|GomezAgent|gonzo1|(?:[a-zA-Z]{1,30}-|)Googlebot(?:-[a-zA-Z]{1,30}|)|Google SketchUp|grub-client|gsa-crawler|heritrix|HiddenMarket|holmes|HooWWWer|htdig|ia_archiver|ICC-Crawler|Icarus6j|ichiro(?:/mobile|)|IconSurf|IlTrovatore(?:-Setaccio|)|InfuzApp|Innovazion Crawler|InternetArchive|IP2[a-z]{1,30}Bot|jbot\\b|KaloogaBot|Kraken|Kurzor|larbin|LEIA|LesnikBot|Linguee Bot|LinkAider|LinkedInBot|Lite Bot|Llaut|lycos|Mail\\.RU_Bot|masscan|masidani_bot|Mediapartners-Google|Microsoft .{0,30} Bot|mogimogi|mozDex|MJ12bot|msnbot(?:-media {0,2}|)|msrbot|Mtps Feed Aggregation System|netresearch|Netvibes|NewsGator[^/]{0,30}|^NING|Nutch[^/]{0,30}|Nymesis|ObjectsSearch|OgScrper|Orbiter|OOZBOT|PagePeeker|PagesInventory|PaxleFramework|Peeplo Screenshot Bot|PlantyNet_WebRobot|Pompos|Qwantify|Read%20Later|Reaper|RedCarpet|Retreiver|Riddler|Rival IQ|scooter|Scrapy|Scrubby|searchsight|seekbot|semanticdiscovery|SemrushBot|Simpy|SimplePie|SEOstats|SimpleRSS|SiteCon|Slackbot-LinkExpanding|Slack-ImgProxy|Slurp|snappy|Speedy Spider|Squrl Java|Stringer|TheUsefulbot|ThumbShotsBot|Thumbshots\\.ru|Tiny Tiny RSS|Twitterbot|WhatsApp|URL2PNG|Vagabondo|VoilaBot|^vortex|Votay bot|^voyager|WASALive.Bot|Web-sniffer|WebThumb|WeSEE:[A-z]{1,30}|WhatWeb|WIRE|WordPress|Wotbox|www\\.almaden\\.ibm\\.com|Xenu(?:.s|) Link Sleuth|Xerka [A-z]{1,30}Bot|yacy(?:bot|)|YahooSeeker|Yahoo! Slurp|Yandex\\w{1,30}|YodaoBot(?:-[A-z]{1,30}|)|YottaaMonitor|Yowedo|^Zao|^Zao-Crawler|ZeBot_www\\.ze\\.bz|ZooShot|ZyBorg|ArcGIS Hub Indexer)(?:[ /]v?(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)|)@'],
        ['regex' => '@\\b(Boto3?|JetS3t|aws-(?:cli|sdk-(?:cpp|go|java|nodejs|ruby2?|dotnet-(?:\\d{1,2}|core)))|s3fs)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        ['regex' => '@(FME)\\/(\\d+\\.\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@(QGIS)\\/(\\d)\\.?0?(\\d{1,2})\\.?0?(\\d{1,2})@'],
        ['regex' => '@(JOSM)/(\\d+)\\.(\\d+)@'],
        ['regex' => '@(Tygron Platform) \\((\\d+)\\.(\\d+)\\.(\\d+(?:\\.\\d+| RC \\d+\\.\\d+))@'],
        [
            'regex' => '@\\[(FBAN/MessengerForiOS|FB_IAB/MESSENGER);FBAV/(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)@',
            'family_replacement' => 'Facebook Messenger',
        ],
        [
            'regex' => '@\\[FB.*;(FBAV)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Facebook',
        ],
        [
            'regex' => '@\\[FB.*;@',
            'family_replacement' => 'Facebook',
        ],
        ['regex' => '@(?:\\/[A-Za-z0-9\\.]+|) {0,5}([A-Za-z0-9 \\-_\\!\\[\\]:]{0,50}(?:[Aa]rchiver|[Ii]ndexer|[Ss]craper|[Bb]ot|[Ss]pider|[Cc]rawl[a-z]{0,50}))[/ ](\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)@'],
        ['regex' => '@((?:[A-Za-z][A-Za-z0-9 -]{0,50}|)[^C][^Uu][Bb]ot)\\b(?:(?:[ /]| v)(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)@'],
        ['regex' => '@((?:[A-z0-9]{1,50}|[A-z\\-]{1,50} ?|)(?: the |)(?:[Ss][Pp][Ii][Dd][Ee][Rr]|[Ss]crape|[Cc][Rr][Aa][Ww][Ll])[A-z0-9]{0,50})(?:(?:[ /]| v)(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)@'],
        ['regex' => '@(HbbTV)/(\\d+)\\.(\\d+)\\.(\\d+) \\(@'],
        ['regex' => '@(Chimera|SeaMonkey|Camino|Waterfox)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+[a-z]*|)@'],
        [
            'regex' => '@(SailfishBrowser)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Sailfish Browser',
        ],
        ['regex' => '@\\[(Pinterest)/[^\\]]+\\]@'],
        ['regex' => '@(Pinterest)(?: for Android(?: Tablet|)|)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        ['regex' => '@Mozilla.*Mobile.*(Instagram).(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@Mozilla.*Mobile.*(Flipboard).(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@Mozilla.*Mobile.*(Flipboard-Briefing).(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@Mozilla.*Mobile.*(Onefootball)\\/Android.(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@(Snapchat)\\/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Twitter for (?:iPhone|iPad)|TwitterAndroid)(?:\\/(\\d+)\\.(\\d+)|)@',
            'family_replacement' => 'Twitter',
        ],
        [
            'regex' => '@Mozilla.*Mobile.*AspiegelBot@',
            'family_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
            'model_replacement' => 'Smartphone',
        ],
        [
            'regex' => '@AspiegelBot@',
            'family_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
            'model_replacement' => 'Desktop',
        ],
        [
            'regex' => '@(Firefox)/(\\d+)\\.(\\d+) Basilisk/(\\d+)@',
            'family_replacement' => 'Basilisk',
        ],
        [
            'regex' => '@(PaleMoon)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Pale Moon',
        ],
        [
            'regex' => '@(Fennec)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+[a-z]*)@',
            'family_replacement' => 'Firefox Mobile',
        ],
        [
            'regex' => '@(Fennec)/(\\d+)\\.(\\d+)(pre)@',
            'family_replacement' => 'Firefox Mobile',
        ],
        [
            'regex' => '@(Fennec)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Firefox Mobile',
        ],
        [
            'regex' => '@(?:Mobile|Tablet);.*(Firefox)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Firefox Mobile',
        ],
        [
            'regex' => '@(Namoroka|Shiretoko|Minefield)/(\\d+)\\.(\\d+)\\.(\\d+(?:pre|))@',
            'family_replacement' => 'Firefox ($1)',
        ],
        [
            'regex' => '@(Firefox)/(\\d+)\\.(\\d+)(a\\d+[a-z]*)@',
            'family_replacement' => 'Firefox Alpha',
        ],
        [
            'regex' => '@(Firefox)/(\\d+)\\.(\\d+)(b\\d+[a-z]*)@',
            'family_replacement' => 'Firefox Beta',
        ],
        [
            'regex' => '@(Firefox)-(?:\\d+\\.\\d+|)/(\\d+)\\.(\\d+)(a\\d+[a-z]*)@',
            'family_replacement' => 'Firefox Alpha',
        ],
        [
            'regex' => '@(Firefox)-(?:\\d+\\.\\d+|)/(\\d+)\\.(\\d+)(b\\d+[a-z]*)@',
            'family_replacement' => 'Firefox Beta',
        ],
        [
            'regex' => '@(Namoroka|Shiretoko|Minefield)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*|)@',
            'family_replacement' => 'Firefox ($1)',
        ],
        [
            'regex' => '@(Firefox).*Tablet browser (\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'MicroB',
        ],
        ['regex' => '@(MozillaDeveloperPreview)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*|)@'],
        [
            'regex' => '@(FxiOS)/(\\d+)\\.(\\d+)(\\.(\\d+)|)(\\.(\\d+)|)@',
            'family_replacement' => 'Firefox iOS',
        ],
        ['regex' => '@(Flock)/(\\d+)\\.(\\d+)(b\\d+?)@'],
        ['regex' => '@(RockMelt)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Navigator)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Netscape',
        ],
        [
            'regex' => '@(Navigator)/(\\d+)\\.(\\d+)([ab]\\d+)@',
            'family_replacement' => 'Netscape',
        ],
        [
            'regex' => '@(Netscape6)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+|)@',
            'family_replacement' => 'Netscape',
        ],
        [
            'regex' => '@(MyIBrow)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'My Internet Browser',
        ],
        [
            'regex' => '@(UC? ?Browser|UCWEB|U3)[ /]?(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'UC Browser',
        ],
        ['regex' => '@(Opera Tablet).*Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        ['regex' => '@(Opera Mini)(?:/att|)/?(\\d+|)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@(Opera)/.+Opera Mobi.+Version/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Opera Mobile',
        ],
        [
            'regex' => '@(Opera)/(\\d+)\\.(\\d+).+Opera Mobi@',
            'family_replacement' => 'Opera Mobile',
        ],
        [
            'regex' => '@Opera Mobi.+(Opera)(?:/|\\s+)(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Opera Mobile',
        ],
        [
            'regex' => '@Opera Mobi@',
            'family_replacement' => 'Opera Mobile',
        ],
        ['regex' => '@(Opera)/9.80.*Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@(?:Mobile Safari).*(OPR)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Opera Mobile',
        ],
        [
            'regex' => '@(?:Chrome).*(OPR)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Opera',
        ],
        [
            'regex' => '@(Coast)/(\\d+).(\\d+).(\\d+)@',
            'family_replacement' => 'Opera Coast',
        ],
        [
            'regex' => '@(OPiOS)/(\\d+).(\\d+).(\\d+)@',
            'family_replacement' => 'Opera Mini',
        ],
        [
            'regex' => '@Chrome/.+( MMS)/(\\d+).(\\d+).(\\d+)@',
            'family_replacement' => 'Opera Neon',
        ],
        [
            'regex' => '@(hpw|web)OS/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'webOS Browser',
        ],
        [
            'regex' => '@(luakit)@',
            'family_replacement' => 'LuaKit',
        ],
        ['regex' => '@(Snowshoe)/(\\d+)\\.(\\d+).(\\d+)@'],
        ['regex' => '@Gecko/\\d+ (Lightning)/(\\d+)\\.(\\d+)\\.?((?:[ab]?\\d+[a-z]*)|(?:\\d*))@'],
        [
            'regex' => '@(Firefox)/(\\d+)\\.(\\d+)\\.(\\d+(?:pre|)) \\(Swiftfox\\)@',
            'family_replacement' => 'Swiftfox',
        ],
        [
            'regex' => '@(Firefox)/(\\d+)\\.(\\d+)([ab]\\d+[a-z]*|) \\(Swiftfox\\)@',
            'family_replacement' => 'Swiftfox',
        ],
        [
            'regex' => '@(rekonq)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|) Safari@',
            'family_replacement' => 'Rekonq',
        ],
        [
            'regex' => '@rekonq@',
            'family_replacement' => 'Rekonq',
        ],
        [
            'regex' => '@(conkeror|Conkeror)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Conkeror',
        ],
        [
            'regex' => '@(konqueror)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Konqueror',
        ],
        ['regex' => '@(WeTab)-Browser@'],
        [
            'regex' => '@(Comodo_Dragon)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Comodo Dragon',
        ],
        ['regex' => '@(Symphony) (\\d+).(\\d+)@'],
        [
            'regex' => '@PLAYSTATION 3.+WebKit@',
            'family_replacement' => 'NetFront NX',
        ],
        [
            'regex' => '@PLAYSTATION 3@',
            'family_replacement' => 'NetFront',
        ],
        [
            'regex' => '@(PlayStation Portable)@',
            'family_replacement' => 'NetFront',
        ],
        [
            'regex' => '@(PlayStation Vita)@',
            'family_replacement' => 'NetFront NX',
        ],
        [
            'regex' => '@AppleWebKit.+ (NX)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'NetFront NX',
        ],
        [
            'regex' => '@(Nintendo 3DS)@',
            'family_replacement' => 'NetFront NX',
        ],
        [
            'regex' => '@(Silk)/(\\d+)\\.(\\d+)(?:\\.([0-9\\-]+)|)@',
            'family_replacement' => 'Amazon Silk',
        ],
        ['regex' => '@(Puffin)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@Windows Phone .*(Edge)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Edge Mobile',
        ],
        [
            'regex' => '@(EdgiOS|EdgA)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Edge Mobile',
        ],
        [
            'regex' => '@(SamsungBrowser)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Samsung Internet',
        ],
        [
            'regex' => '@(SznProhlizec)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Seznam prohlížeč',
        ],
        [
            'regex' => '@(coc_coc_browser)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Coc Coc',
        ],
        [
            'regex' => '@(baidubrowser)[/\\s](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Baidu Browser',
        ],
        [
            'regex' => '@(FlyFlow)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Baidu Explorer',
        ],
        [
            'regex' => '@(MxBrowser)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Maxthon',
        ],
        ['regex' => '@(Crosswalk)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Line)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'LINE',
        ],
        [
            'regex' => '@(MiuiBrowser)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'MiuiBrowser',
        ],
        [
            'regex' => '@(Mint Browser)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Mint Browser',
        ],
        [
            'regex' => '@(TopBuzz)/(\\d+).(\\d+).(\\d+)@',
            'family_replacement' => 'TopBuzz',
        ],
        [
            'regex' => '@Mozilla.+Android.+(GSA)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Google',
        ],
        [
            'regex' => '@(MQQBrowser/Mini)(?:(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)@',
            'family_replacement' => 'QQ Browser Mini',
        ],
        [
            'regex' => '@(MQQBrowser)(?:/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)@',
            'family_replacement' => 'QQ Browser Mobile',
        ],
        [
            'regex' => '@(QQBrowser)(?:/(\\d+)(?:\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)|)@',
            'family_replacement' => 'QQ Browser',
        ],
        [
            'regex' => '@Version/.+(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Chrome Mobile WebView',
        ],
        [
            'regex' => '@; wv\\).+(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Chrome Mobile WebView',
        ],
        [
            'regex' => '@(CrMo)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Chrome Mobile',
        ],
        [
            'regex' => '@(CriOS)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Chrome Mobile iOS',
        ],
        [
            'regex' => '@(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+) Mobile(?:[ /]|$)@',
            'family_replacement' => 'Chrome Mobile',
        ],
        [
            'regex' => '@ Mobile .*(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Chrome Mobile',
        ],
        [
            'regex' => '@(chromeframe)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Chrome Frame',
        ],
        [
            'regex' => '@(SLP Browser)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Tizen Browser',
        ],
        [
            'regex' => '@(SE 2\\.X) MetaSr (\\d+)\\.(\\d+)@',
            'family_replacement' => 'Sogou Explorer',
        ],
        [
            'regex' => '@(Rackspace Monitoring)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'RackspaceBot',
        ],
        ['regex' => '@(PRTG Network Monitor)@'],
        ['regex' => '@(PyAMF)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(YaBrowser)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Yandex Browser',
        ],
        [
            'regex' => '@(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+).* MRCHROME@',
            'family_replacement' => 'Mail.ru Chromium Browser',
        ],
        ['regex' => '@(AOL) (\\d+)\\.(\\d+); AOLBuild (\\d+)@'],
        ['regex' => '@(PodCruncher|Downcast)[ /]?(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        ['regex' => '@ (BoxNotes)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Whale)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+) Mobile(?:[ /]|$)@',
            'family_replacement' => 'Whale',
        ],
        [
            'regex' => '@(Whale)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Whale',
        ],
        ['regex' => '@(1Password)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@(Ghost)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Slack_SSB)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Slack Desktop Client',
        ],
        [
            'regex' => '@(HipChat)/?(\\d+|)@',
            'family_replacement' => 'HipChat Desktop Client',
        ],
        ['regex' => '@\\b(MobileIron|FireWeb|Jasmine|ANTGalio|Midori|Fresco|Lobo|PaleMoon|Maxthon|Lynx|OmniWeb|Dillo|Camino|Demeter|Fluid|Fennec|Epiphany|Shiira|Sunrise|Spotify|Flock|Netscape|Lunascape|WebPilot|NetFront|Netfront|Konqueror|SeaMonkey|Kazehakase|Vienna|Iceape|Iceweasel|IceWeasel|Iron|K-Meleon|Sleipnir|Galeon|GranParadiso|Opera Mini|iCab|NetNewsWire|ThunderBrowse|Iris|UP\\.Browser|Bunjalloo|Google Earth|Raven for Mac|Openwave|MacOutlook|Electron|OktaMobile)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@Microsoft Office Outlook 12\\.\\d+\\.\\d+|MSOffice 12@',
            'family_replacement' => 'Outlook',
            'v1_replacement' => '2007',
        ],
        [
            'regex' => '@Microsoft Outlook 14\\.\\d+\\.\\d+|MSOffice 14@',
            'family_replacement' => 'Outlook',
            'v1_replacement' => '2010',
        ],
        [
            'regex' => '@Microsoft Outlook 15\\.\\d+\\.\\d+@',
            'family_replacement' => 'Outlook',
            'v1_replacement' => '2013',
        ],
        [
            'regex' => '@Microsoft Outlook (?:Mail )?16\\.\\d+\\.\\d+|MSOffice 16@',
            'family_replacement' => 'Outlook',
            'v1_replacement' => '2016',
        ],
        ['regex' => '@Microsoft Office (Word) 2014@'],
        [
            'regex' => '@Outlook-Express\\/7\\.0.*@',
            'family_replacement' => 'Windows Live Mail',
        ],
        ['regex' => '@(Airmail) (\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@(Thunderbird)/(\\d+)\\.(\\d+)(?:\\.(\\d+(?:pre|))|)@',
            'family_replacement' => 'Thunderbird',
        ],
        [
            'regex' => '@(Postbox)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Postbox',
        ],
        [
            'regex' => '@(Barca(?:Pro)?)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Barca',
        ],
        [
            'regex' => '@(Lotus-Notes)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Lotus Notes',
        ],
        [
            'regex' => '@Superhuman@',
            'family_replacement' => 'Superhuman',
        ],
        ['regex' => '@(Vivaldi)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Edge?)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Edge',
        ],
        [
            'regex' => '@(brave)/(\\d+)\\.(\\d+)\\.(\\d+) Chrome@',
            'family_replacement' => 'Brave',
        ],
        [
            'regex' => '@(Chrome)/(\\d+)\\.(\\d+)\\.(\\d+)[\\d.]* Iron[^/]@',
            'family_replacement' => 'Iron',
        ],
        ['regex' => '@\\b(Dolphin)(?: |HDCN/|/INT\\-)(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        ['regex' => '@(HeadlessChrome)(?:/(\\d+)\\.(\\d+)\\.(\\d+)|)@'],
        ['regex' => '@(Evolution)/(\\d+)\\.(\\d+)\\.(\\d+\\.\\d+)@'],
        ['regex' => '@(RCM CardDAV plugin)/(\\d+)\\.(\\d+)\\.(\\d+(?:-dev|))@'],
        ['regex' => '@(bingbot|Bolt|AdobeAIR|Jasmine|IceCat|Skyfire|Midori|Maxthon|Lynx|Arora|IBrowse|Dillo|Camino|Shiira|Fennec|Phoenix|Flock|Netscape|Lunascape|Epiphany|WebPilot|Opera Mini|Opera|NetFront|Netfront|Konqueror|Googlebot|SeaMonkey|Kazehakase|Vienna|Iceape|Iceweasel|IceWeasel|Iron|K-Meleon|Sleipnir|Galeon|GranParadiso|iCab|iTunes|MacAppStore|NetNewsWire|Space Bison|Stainless|Orca|Dolfin|BOLT|Minimo|Tizen Browser|Polaris|Abrowser|Planetweb|ICE Browser|mDolphin|qutebrowser|Otter|QupZilla|MailBar|kmail2|YahooMobileMail|ExchangeWebServices|ExchangeServicesClient|Dragon|Outlook-iOS-Android)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        ['regex' => '@(Chromium|Chrome)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@(IEMobile)[ /](\\d+)\\.(\\d+)@',
            'family_replacement' => 'IE Mobile',
        ],
        ['regex' => '@(BacaBerita App)\\/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@^(bPod|Pocket Casts|Player FM)$@'],
        ['regex' => '@^(AlexaMediaPlayer|VLC)/(\\d+)\\.(\\d+)\\.([^.\\s]+)@'],
        ['regex' => '@^(AntennaPod|WMPlayer|Zune|Podkicker|Radio|ExoPlayerDemo|Overcast|PocketTunes|NSPlayer|okhttp|DoggCatcher|QuickNews|QuickTime|Peapod|Podcasts|GoldenPod|VLC|Spotify|Miro|MediaGo|Juice|iPodder|gPodder|Banshee)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        ['regex' => '@^(Peapod|Liferea)/([^.\\s]+)\\.([^.\\s]+|)\\.?([^.\\s]+|)@'],
        ['regex' => '@^(bPod|Player FM) BMID/(\\S+)@'],
        ['regex' => '@^(Podcast ?Addict)/v(\\d+) @'],
        [
            'regex' => '@^(Podcast ?Addict) @',
            'family_replacement' => 'PodcastAddict',
        ],
        ['regex' => '@(Replay) AV@'],
        ['regex' => '@(VOX) Music Player@'],
        ['regex' => '@(CITA) RSS Aggregator/(\\d+)\\.(\\d+)@'],
        ['regex' => '@(Pocket Casts)$@'],
        ['regex' => '@(Player FM)$@'],
        ['regex' => '@(LG Player|Doppler|FancyMusic|MediaMonkey|Clementine) (\\d+)\\.(\\d+)\\.?([^.\\s]+|)\\.?([^.\\s]+|)@'],
        ['regex' => '@(philpodder)/(\\d+)\\.(\\d+)\\.?([^.\\s]+|)\\.?([^.\\s]+|)@'],
        ['regex' => '@(Player FM|Pocket Casts|DoggCatcher|Spotify|MediaMonkey|MediaGo|BashPodder)@'],
        ['regex' => '@(QuickTime)\\.(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@(Kinoma)(\\d+)@'],
        [
            'regex' => '@(Fancy) Cloud Music (\\d+)\\.(\\d+)@',
            'family_replacement' => 'FancyMusic',
        ],
        [
            'regex' => '@EspnDownloadManager@',
            'family_replacement' => 'ESPN',
        ],
        ['regex' => '@(ESPN) Radio (\\d+)\\.(\\d+)(?:\\.(\\d+)|) ?(?:rv:(\\d+)|) @'],
        ['regex' => '@(podracer|jPodder) v ?(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        ['regex' => '@(ZDM)/(\\d+)\\.(\\d+)[; ]?@'],
        ['regex' => '@(Zune|BeyondPod) (\\d+)(?:\\.(\\d+)|)[\\);]@'],
        ['regex' => '@(WMPlayer)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@^(Lavf)@',
            'family_replacement' => 'WMPlayer',
        ],
        ['regex' => '@^(RSSRadio)[ /]?(\\d+|)@'],
        [
            'regex' => '@(RSS_Radio) (\\d+)\\.(\\d+)@',
            'family_replacement' => 'RSSRadio',
        ],
        [
            'regex' => '@(Podkicker) \\S+/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Podkicker',
        ],
        ['regex' => '@^(HTC) Streaming Player \\S+ / \\S+ / \\S+ / (\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        ['regex' => '@^(Stitcher)/iOS@'],
        ['regex' => '@^(Stitcher)/Android@'],
        ['regex' => '@^(VLC) .*version (\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@ (VLC) for@'],
        [
            'regex' => '@(vlc)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'VLC',
        ],
        ['regex' => '@^(foobar)\\S+/([^.\\s]+)\\.([^.\\s]+|)\\.?([^.\\s]+|)@'],
        ['regex' => '@^(Clementine)\\S+ ([^.\\s]+)\\.([^.\\s]+|)\\.?([^.\\s]+|)@'],
        [
            'regex' => '@(amarok)/([^.\\s]+)\\.([^.\\s]+|)\\.?([^.\\s]+|)@',
            'family_replacement' => 'Amarok',
        ],
        ['regex' => '@(Custom)-Feed Reader@'],
        ['regex' => '@(iRider|Crazy Browser|SkipStone|iCab|Lunascape|Sleipnir|Maemo Browser) (\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@(iCab|Lunascape|Opera|Android|Jasmine|Polaris|Microsoft SkyDriveSync|The Bat!) (\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        ['regex' => '@(Kindle)/(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Android) Donut@',
            'v1_replacement' => '1',
            'v2_replacement' => '2',
        ],
        [
            'regex' => '@(Android) Eclair@',
            'v1_replacement' => '2',
            'v2_replacement' => '1',
        ],
        [
            'regex' => '@(Android) Froyo@',
            'v1_replacement' => '2',
            'v2_replacement' => '2',
        ],
        [
            'regex' => '@(Android) Gingerbread@',
            'v1_replacement' => '2',
            'v2_replacement' => '3',
        ],
        [
            'regex' => '@(Android) Honeycomb@',
            'v1_replacement' => '3',
        ],
        [
            'regex' => '@(MSIE) (\\d+)\\.(\\d+).*XBLWP7@',
            'family_replacement' => 'IE Large Screen',
        ],
        ['regex' => '@(Nextcloud)@'],
        ['regex' => '@(mirall)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(ownCloud-android)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Owncloud',
        ],
        [
            'regex' => '@(OC)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+) \\(Skype for Business\\)@',
            'family_replacement' => 'Skype',
        ],
        ['regex' => '@(Obigo)InternetBrowser@'],
        ['regex' => '@(Obigo)\\-Browser@'],
        [
            'regex' => '@(Obigo|OBIGO)[^\\d]*(\\d+)(?:.(\\d+)|)@',
            'family_replacement' => 'Obigo',
        ],
        [
            'regex' => '@(MAXTHON|Maxthon) (\\d+)\\.(\\d+)@',
            'family_replacement' => 'Maxthon',
        ],
        [
            'regex' => '@(Maxthon|MyIE2|Uzbl|Shiira)@',
            'v1_replacement' => '0',
        ],
        ['regex' => '@(BrowseX) \\((\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(NCSA_Mosaic)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'NCSA Mosaic',
        ],
        [
            'regex' => '@(POLARIS)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Polaris',
        ],
        [
            'regex' => '@(Embider)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Polaris',
        ],
        [
            'regex' => '@(BonEcho)/(\\d+)\\.(\\d+)\\.?([ab]?\\d+|)@',
            'family_replacement' => 'Bon Echo',
        ],
        [
            'regex' => '@(TopBuzz) com.alex.NewsMaster/(\\d+).(\\d+).(\\d+)@',
            'family_replacement' => 'TopBuzz',
        ],
        [
            'regex' => '@(TopBuzz) com.mobilesrepublic.newsrepublic/(\\d+).(\\d+).(\\d+)@',
            'family_replacement' => 'TopBuzz',
        ],
        [
            'regex' => '@(TopBuzz) com.topbuzz.videoen/(\\d+).(\\d+).(\\d+)@',
            'family_replacement' => 'TopBuzz',
        ],
        [
            'regex' => '@(iPod|iPhone|iPad).+GSA/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|) Mobile@',
            'family_replacement' => 'Google',
        ],
        [
            'regex' => '@(iPod|iPhone|iPad).+Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|).*[ +]Safari@',
            'family_replacement' => 'Mobile Safari',
        ],
        [
            'regex' => '@(iPod|iPod touch|iPhone|iPad);.*CPU.*OS[ +](\\d+)_(\\d+)(?:_(\\d+)|).* AppleNews\\/\\d+\\.\\d+\\.\\d+?@',
            'family_replacement' => 'Mobile Safari UI/WKWebView',
        ],
        [
            'regex' => '@(iPod|iPhone|iPad).+Version/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'family_replacement' => 'Mobile Safari UI/WKWebView',
        ],
        [
            'regex' => '@(iPod|iPod touch|iPhone|iPad).* Safari@',
            'family_replacement' => 'Mobile Safari',
        ],
        [
            'regex' => '@(iPod|iPod touch|iPhone|iPad)@',
            'family_replacement' => 'Mobile Safari UI/WKWebView',
        ],
        [
            'regex' => '@(Watch)(\\d+),(\\d+)@',
            'family_replacement' => 'Apple $1 App',
        ],
        ['regex' => '@(Outlook-iOS)/\\d+\\.\\d+\\.prod\\.iphone \\((\\d+)\\.(\\d+)\\.(\\d+)\\)@'],
        ['regex' => '@(AvantGo) (\\d+).(\\d+)@'],
        [
            'regex' => '@(OneBrowser)/(\\d+).(\\d+)@',
            'family_replacement' => 'ONE Browser',
        ],
        [
            'regex' => '@(Avant)@',
            'v1_replacement' => '1',
        ],
        [
            'regex' => '@(QtCarBrowser)@',
            'v1_replacement' => '1',
        ],
        [
            'regex' => '@^(iBrowser/Mini)(\\d+).(\\d+)@',
            'family_replacement' => 'iBrowser Mini',
        ],
        ['regex' => '@^(iBrowser|iRAPP)/(\\d+).(\\d+)@'],
        [
            'regex' => '@^(Nokia)@',
            'family_replacement' => 'Nokia Services (WAP) Browser',
        ],
        [
            'regex' => '@(NokiaBrowser)/(\\d+)\\.(\\d+).(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Nokia Browser',
        ],
        [
            'regex' => '@(NokiaBrowser)/(\\d+)\\.(\\d+).(\\d+)@',
            'family_replacement' => 'Nokia Browser',
        ],
        [
            'regex' => '@(NokiaBrowser)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Nokia Browser',
        ],
        [
            'regex' => '@(BrowserNG)/(\\d+)\\.(\\d+).(\\d+)@',
            'family_replacement' => 'Nokia Browser',
        ],
        [
            'regex' => '@(Series60)/5\\.0@',
            'family_replacement' => 'Nokia Browser',
            'v1_replacement' => '7',
            'v2_replacement' => '0',
        ],
        [
            'regex' => '@(Series60)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Nokia OSS Browser',
        ],
        [
            'regex' => '@(S40OviBrowser)/(\\d+)\\.(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Ovi Browser',
        ],
        ['regex' => '@(Nokia)[EN]?(\\d+)@'],
        [
            'regex' => '@(PlayBook).+RIM Tablet OS (\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'BlackBerry WebKit',
        ],
        [
            'regex' => '@(Black[bB]erry|BB10).+Version/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'BlackBerry WebKit',
        ],
        [
            'regex' => '@(Black[bB]erry)\\s?(\\d+)@',
            'family_replacement' => 'BlackBerry',
        ],
        ['regex' => '@(OmniWeb)/v(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Blazer)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Palm Blazer',
        ],
        [
            'regex' => '@(Pre)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Palm Pre',
        ],
        ['regex' => '@(ELinks)/(\\d+)\\.(\\d+)@'],
        ['regex' => '@(ELinks) \\((\\d+)\\.(\\d+)@'],
        ['regex' => '@(Links) \\((\\d+)\\.(\\d+)@'],
        ['regex' => '@(QtWeb) Internet Browser/(\\d+)\\.(\\d+)@'],
        ['regex' => '@(PhantomJS)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(AppleWebKit)/(\\d+)(?:\\.(\\d+)|)\\+ .* Safari@',
            'family_replacement' => 'WebKit Nightly',
        ],
        [
            'regex' => '@(Version)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|).*Safari/@',
            'family_replacement' => 'Safari',
        ],
        ['regex' => '@(Safari)/\\d+@'],
        ['regex' => '@(OLPC)/Update(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(OLPC)/Update()\\.(\\d+)@',
            'v1_replacement' => '0',
        ],
        ['regex' => '@(SEMC\\-Browser)/(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Teleca)@',
            'family_replacement' => 'Teleca Browser',
        ],
        [
            'regex' => '@(Phantom)/V(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Phantom Browser',
        ],
        [
            'regex' => '@(Trident)/(7|8)\\.(0)@',
            'family_replacement' => 'IE',
            'v1_replacement' => '11',
        ],
        [
            'regex' => '@(Trident)/(6)\\.(0)@',
            'family_replacement' => 'IE',
            'v1_replacement' => '10',
        ],
        [
            'regex' => '@(Trident)/(5)\\.(0)@',
            'family_replacement' => 'IE',
            'v1_replacement' => '9',
        ],
        [
            'regex' => '@(Trident)/(4)\\.(0)@',
            'family_replacement' => 'IE',
            'v1_replacement' => '8',
        ],
        ['regex' => '@(Espial)/(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@(AppleWebKit)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Apple Mail',
        ],
        ['regex' => '@(Firefox)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@(Firefox)/(\\d+)\\.(\\d+)(pre|[ab]\\d+[a-z]*|)@'],
        [
            'regex' => '@([MS]?IE) (\\d+)\\.(\\d+)@',
            'family_replacement' => 'IE',
        ],
        [
            'regex' => '@(python-requests)/(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Python Requests',
        ],
        ['regex' => '@\\b(Windows-Update-Agent|WindowsPowerShell|Microsoft-CryptoAPI|SophosUpdateManager|SophosAgent|Debian APT-HTTP|Ubuntu APT-HTTP|libcurl-agent|libwww-perl|urlgrabber|curl|PycURL|Wget|wget2|aria2|Axel|OpenBSD ftp|lftp|jupdate|insomnia|fetch libfetch|akka-http|got)(?:[ /](\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)|)@'],
        [
            'regex' => '@(Python/3\\.\\d{1,3} aiohttp)/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Python aiohttp',
        ],
        ['regex' => '@(Java)[/ ]?\\d+\\.(\\d+)\\.(\\d+)[_-]*([a-zA-Z0-9]+|)@'],
        ['regex' => '@^(Cyberduck)/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.\\d+|)@'],
        ['regex' => '@^(S3 Browser) (\\d+)-(\\d+)-(\\d+)(?:\\s*http://s3browser\\.com|)@'],
        ['regex' => '@(S3Gof3r)@'],
        ['regex' => '@\\b(ibm-cos-sdk-(?:core|java|js|python))/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@'],
        ['regex' => '@^(rusoto)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@^(rclone)/v(\\d+)\\.(\\d+)@'],
        ['regex' => '@^(Roku)/DVP-(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(Kurio)\\/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'Kurio App',
        ],
        ['regex' => '@^(Box(?: Sync)?)/(\\d+)\\.(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@^(ViaFree|Viafree)-(?:tvOS-)?[A-Z]{2}/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'family_replacement' => 'ViaFree',
        ],
    ],
    'os_parsers' => [
        [
            'regex' => '@HbbTV/\\d+\\.\\d+\\.\\d+ \\( ;(LG)E ;NetCast 4.0@',
            'os_v1_replacement' => '2013',
        ],
        [
            'regex' => '@HbbTV/\\d+\\.\\d+\\.\\d+ \\( ;(LG)E ;NetCast 3.0@',
            'os_v1_replacement' => '2012',
        ],
        [
            'regex' => '@HbbTV/1.1.1 \\(;;;;;\\) Maple_2011@',
            'os_replacement' => 'Samsung',
            'os_v1_replacement' => '2011',
        ],
        [
            'regex' => '@HbbTV/\\d+\\.\\d+\\.\\d+ \\(;(Samsung);SmartTV([0-9]{4});.*FXPDEUC@',
            'os_v2_replacement' => 'UE40F7000',
        ],
        [
            'regex' => '@HbbTV/\\d+\\.\\d+\\.\\d+ \\(;(Samsung);SmartTV([0-9]{4});.*MST12DEUC@',
            'os_v2_replacement' => 'UE32F4500',
        ],
        [
            'regex' => '@HbbTV/1\\.1\\.1 \\(; (Philips);.*NETTV/4@',
            'os_v1_replacement' => '2013',
        ],
        [
            'regex' => '@HbbTV/1\\.1\\.1 \\(; (Philips);.*NETTV/3@',
            'os_v1_replacement' => '2012',
        ],
        [
            'regex' => '@HbbTV/1\\.1\\.1 \\(; (Philips);.*NETTV/2@',
            'os_v1_replacement' => '2011',
        ],
        [
            'regex' => '@HbbTV/\\d+\\.\\d+\\.\\d+.*(firetv)-firefox-plugin (\\d+).(\\d+).(\\d+)@',
            'os_replacement' => 'FireHbbTV',
        ],
        ['regex' => '@HbbTV/\\d+\\.\\d+\\.\\d+ \\(.*; ?([a-zA-Z]+) ?;.*(201[1-9]).*\\)@'],
        [
            'regex' => '@AspiegelBot@',
            'os_replacement' => 'Other',
        ],
        ['regex' => '@(Windows Phone) (?:OS[ /])?(\\d+)\\.(\\d+)@'],
        [
            'regex' => '@(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone)[ +]+(\\d+)[_\\.](\\d+)(?:[_\\.](\\d+)|).*Outlook-iOS-Android@',
            'os_replacement' => 'iOS',
        ],
        ['regex' => '@ArcGIS\\.?(iOS|Android)-\\d+\\.\\d+(?:\\.\\d+|)(?:[^\\/]+|)\\/(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)@'],
        ['regex' => '@ArcGISRuntime-(?:Android|iOS)\\/\\d+\\.\\d+(?:\\.\\d+|) \\((Android|iOS) (\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|);@'],
        ['regex' => '@(Android)[ \\-/](\\d+)(?:\\.(\\d+)|)(?:[.\\-]([a-z0-9]+)|)@'],
        [
            'regex' => '@(Android) Donut@',
            'os_v1_replacement' => '1',
            'os_v2_replacement' => '2',
        ],
        [
            'regex' => '@(Android) Eclair@',
            'os_v1_replacement' => '2',
            'os_v2_replacement' => '1',
        ],
        [
            'regex' => '@(Android) Froyo@',
            'os_v1_replacement' => '2',
            'os_v2_replacement' => '2',
        ],
        [
            'regex' => '@(Android) Gingerbread@',
            'os_v1_replacement' => '2',
            'os_v2_replacement' => '3',
        ],
        [
            'regex' => '@(Android) Honeycomb@',
            'os_v1_replacement' => '3',
        ],
        ['regex' => '@(Android) (\\d+);@'],
        [
            'regex' => '@^UCWEB.*; (Adr) (\\d+)\\.(\\d+)(?:[.\\-]([a-z0-9]+)|);@',
            'os_replacement' => 'Android',
        ],
        [
            'regex' => '@^UCWEB.*; (iPad|iPh|iPd) OS (\\d+)_(\\d+)(?:_(\\d+)|);@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@^UCWEB.*; (wds) (\\d+)\\.(\\d+)(?:\\.(\\d+)|);@',
            'os_replacement' => 'Windows Phone',
        ],
        [
            'regex' => '@^(JUC).*; ?U; ?(?:Android|)(\\d+)\\.(\\d+)(?:[\\.\\-]([a-z0-9]+)|)@',
            'os_replacement' => 'Android',
        ],
        [
            'regex' => '@(android)\\s(?:mobile\\/)(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)|)|)@',
            'os_replacement' => 'Android',
        ],
        [
            'regex' => '@(Silk-Accelerated=[a-z]{4,5})@',
            'os_replacement' => 'Android',
        ],
        [
            'regex' => '@(x86_64|aarch64)\\ (\\d+)\\.(\\d+)\\.(\\d+).*Chrome.*(?:CitrixChromeApp)$@',
            'os_replacement' => 'Chrome OS',
        ],
        [
            'regex' => '@(XBLWP7)@',
            'os_replacement' => 'Windows Phone',
        ],
        [
            'regex' => '@(Windows ?Mobile)@',
            'os_replacement' => 'Windows Mobile',
        ],
        [
            'regex' => '@(Windows 10)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '10',
        ],
        [
            'regex' => '@(Windows (?:NT 5\\.2|NT 5\\.1))@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => 'XP',
        ],
        [
            'regex' => '@(Win(?:dows NT |32NT\\/)6\\.1)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '7',
        ],
        [
            'regex' => '@(Win(?:dows NT |32NT\\/)6\\.0)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => 'Vista',
        ],
        [
            'regex' => '@(Win 9x 4\\.90)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => 'ME',
        ],
        [
            'regex' => '@(Windows NT 6\\.2; ARM;)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => 'RT',
        ],
        [
            'regex' => '@(Win(?:dows NT |32NT\\/)6\\.2)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '8',
        ],
        [
            'regex' => '@(Windows NT 6\\.3; ARM;)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => 'RT 8',
            'os_v2_replacement' => '1',
        ],
        [
            'regex' => '@(Win(?:dows NT |32NT\\/)6\\.3)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '8',
            'os_v2_replacement' => '1',
        ],
        [
            'regex' => '@(Win(?:dows NT |32NT\\/)6\\.4)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '10',
        ],
        [
            'regex' => '@(Windows NT 10\\.0)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '10',
        ],
        [
            'regex' => '@(Windows NT 5\\.0)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '2000',
        ],
        [
            'regex' => '@(WinNT4.0)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => 'NT 4.0',
        ],
        [
            'regex' => '@(Windows ?CE)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => 'CE',
        ],
        [
            'regex' => '@Win(?:dows)? ?(95|98|3.1|NT|ME|2000|XP|Vista|7|CE)@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '$1',
        ],
        [
            'regex' => '@Win16@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '3.1',
        ],
        [
            'regex' => '@Win32@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '95',
        ],
        [
            'regex' => '@^Box.*Windows/([\\d.]+);@',
            'os_replacement' => 'Windows',
            'os_v1_replacement' => '$1',
        ],
        ['regex' => '@(Tizen)[/ ](\\d+)\\.(\\d+)@'],
        [
            'regex' => '@((?:Mac[ +]?|; )OS[ +]X)[\\s+/](?:(\\d+)[_.](\\d+)(?:[_.](\\d+)|)|Mach-O)@',
            'os_replacement' => 'Mac OS X',
        ],
        [
            'regex' => '@\\w+\\s+Mac OS X\\s+\\w+\\s+(\\d+).(\\d+).(\\d+).*@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '$1',
            'os_v2_replacement' => '$2',
            'os_v3_replacement' => '$3',
        ],
        [
            'regex' => '@ (Dar)(win)/(9).(\\d+).*\\((?:i386|x86_64|Power Macintosh)\\)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '5',
        ],
        [
            'regex' => '@ (Dar)(win)/(10).(\\d+).*\\((?:i386|x86_64)\\)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '6',
        ],
        [
            'regex' => '@ (Dar)(win)/(11).(\\d+).*\\((?:i386|x86_64)\\)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '7',
        ],
        [
            'regex' => '@ (Dar)(win)/(12).(\\d+).*\\((?:i386|x86_64)\\)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '8',
        ],
        [
            'regex' => '@ (Dar)(win)/(13).(\\d+).*\\((?:i386|x86_64)\\)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '9',
        ],
        [
            'regex' => '@Mac_PowerPC@',
            'os_replacement' => 'Mac OS',
        ],
        ['regex' => '@(?:PPC|Intel) (Mac OS X)@'],
        [
            'regex' => '@^Box.*;(Darwin)/(10)\\.(1\\d)(?:\\.(\\d+)|)@',
            'os_replacement' => 'Mac OS X',
        ],
        [
            'regex' => '@(Apple\\s?TV)(?:/(\\d+)\\.(\\d+)|)@',
            'os_replacement' => 'ATV OS X',
        ],
        [
            'regex' => '@(CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone|CPU IPhone OS)[ +]+(\\d+)[_\\.](\\d+)(?:[_\\.](\\d+)|)@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(iPhone|iPad|iPod); Opera@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(iPhone|iPad|iPod).*Mac OS X.*Version/(\\d+)\\.(\\d+)@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/(5)48\\.0\\.3.* Darwin/11\\.0\\.0@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/(5)48\\.(0)\\.4.* Darwin/(1)1\\.0\\.0@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/(5)48\\.(1)\\.4@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/(4)85\\.1(3)\\.9@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/(6)09\\.(1)\\.4@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/(6)(0)9@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/6(7)2\\.(1)\\.13@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/6(7)2\\.(1)\\.(1)4@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CF)(Network)/6(7)(2)\\.1\\.15@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '7',
            'os_v2_replacement' => '1',
        ],
        [
            'regex' => '@(CFNetwork)/6(7)2\\.(0)\\.(?:2|8)@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(CFNetwork)/709\\.1@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '8',
            'os_v2_replacement' => '0.b5',
        ],
        [
            'regex' => '@(CF)(Network)/711\\.(\\d)@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '8',
        ],
        [
            'regex' => '@(CF)(Network)/(720)\\.(\\d)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '10',
        ],
        [
            'regex' => '@(CF)(Network)/(760)\\.(\\d)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '11',
        ],
        [
            'regex' => '@CFNetwork/7.* Darwin/15\\.4\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '9',
            'os_v2_replacement' => '3',
            'os_v3_replacement' => '1',
        ],
        [
            'regex' => '@CFNetwork/7.* Darwin/15\\.5\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '9',
            'os_v2_replacement' => '3',
            'os_v3_replacement' => '2',
        ],
        [
            'regex' => '@CFNetwork/7.* Darwin/15\\.6\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '9',
            'os_v2_replacement' => '3',
            'os_v3_replacement' => '5',
        ],
        [
            'regex' => '@(CF)(Network)/758\\.(\\d)@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '9',
        ],
        [
            'regex' => '@CFNetwork/808\\.3 Darwin/16\\.3\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '2',
            'os_v3_replacement' => '1',
        ],
        [
            'regex' => '@(CF)(Network)/808\\.(\\d)@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '10',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/17\\.\\d+.*\\(x86_64\\)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '13',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/16\\.\\d+.*\\(x86_64\\)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '12',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/15\\.\\d+.*\\(x86_64\\)@',
            'os_replacement' => 'Mac OS X',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '11',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/(9)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '1',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/(10)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '4',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/(11)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '5',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/(13)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '6',
        ],
        [
            'regex' => '@CFNetwork/6.* Darwin/(14)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '7',
        ],
        [
            'regex' => '@CFNetwork/7.* Darwin/(14)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '8',
            'os_v2_replacement' => '0',
        ],
        [
            'regex' => '@CFNetwork/7.* Darwin/(15)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '9',
            'os_v2_replacement' => '0',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/16\\.5\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '3',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/16\\.6\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '3',
            'os_v3_replacement' => '2',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/16\\.7\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '10',
            'os_v2_replacement' => '3',
            'os_v3_replacement' => '3',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/(16)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '10',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/17\\.0\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '11',
            'os_v2_replacement' => '0',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/17\\.2\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '11',
            'os_v2_replacement' => '1',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/17\\.3\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '11',
            'os_v2_replacement' => '2',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/17\\.4\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '11',
            'os_v2_replacement' => '2',
            'os_v3_replacement' => '6',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/17\\.5\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '11',
            'os_v2_replacement' => '3',
        ],
        [
            'regex' => '@CFNetwork/9.* Darwin/17\\.6\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '11',
            'os_v2_replacement' => '4',
        ],
        [
            'regex' => '@CFNetwork/9.* Darwin/17\\.7\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '11',
            'os_v2_replacement' => '4',
            'os_v3_replacement' => '1',
        ],
        [
            'regex' => '@CFNetwork/8.* Darwin/(17)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '11',
        ],
        [
            'regex' => '@CFNetwork/9.* Darwin/18\\.0\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '12',
            'os_v2_replacement' => '0',
        ],
        [
            'regex' => '@CFNetwork/9.* Darwin/(18)\\.\\d+@',
            'os_replacement' => 'iOS',
            'os_v1_replacement' => '12',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@\\b(iOS[ /]|iOS; |iPhone(?:/| v|[ _]OS[/,]|; | OS : |\\d,\\d/|\\d,\\d; )|iPad/)(\\d{1,2})[_\\.](\\d{1,2})(?:[_\\.](\\d+)|)@',
            'os_replacement' => 'iOS',
        ],
        ['regex' => '@\\((iOS);@'],
        [
            'regex' => '@(watchOS)/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'os_replacement' => 'WatchOS',
        ],
        ['regex' => '@Outlook-(iOS)/\\d+\\.\\d+\\.prod\\.iphone@'],
        [
            'regex' => '@(iPod|iPhone|iPad)@',
            'os_replacement' => 'iOS',
        ],
        [
            'regex' => '@(tvOS)[/ ](\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'os_replacement' => 'tvOS',
        ],
        [
            'regex' => '@(CrOS) [a-z0-9_]+ (\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'os_replacement' => 'Chrome OS',
        ],
        [
            'regex' => '@([Dd]ebian)@',
            'os_replacement' => 'Debian',
        ],
        ['regex' => '@(Linux Mint)(?:/(\\d+)|)@'],
        ['regex' => '@(Mandriva)(?: Linux|)/(?:[\\d.-]+m[a-z]{2}(\\d+).(\\d)|)@'],
        [
            'regex' => '@(Symbian[Oo][Ss])[/ ](\\d+)\\.(\\d+)@',
            'os_replacement' => 'Symbian OS',
        ],
        [
            'regex' => '@(Symbian/3).+NokiaBrowser/7\\.3@',
            'os_replacement' => 'Symbian^3 Anna',
        ],
        [
            'regex' => '@(Symbian/3).+NokiaBrowser/7\\.4@',
            'os_replacement' => 'Symbian^3 Belle',
        ],
        [
            'regex' => '@(Symbian/3)@',
            'os_replacement' => 'Symbian^3',
        ],
        [
            'regex' => '@\\b(Series 60|SymbOS|S60Version|S60V\\d|S60\\b)@',
            'os_replacement' => 'Symbian OS',
        ],
        ['regex' => '@(MeeGo)@'],
        [
            'regex' => '@Symbian [Oo][Ss]@',
            'os_replacement' => 'Symbian OS',
        ],
        [
            'regex' => '@Series40;@',
            'os_replacement' => 'Nokia Series 40',
        ],
        [
            'regex' => '@Series30Plus;@',
            'os_replacement' => 'Nokia Series 30 Plus',
        ],
        [
            'regex' => '@(BB10);.+Version/(\\d+)\\.(\\d+)\\.(\\d+)@',
            'os_replacement' => 'BlackBerry OS',
        ],
        [
            'regex' => '@(Black[Bb]erry)[0-9a-z]+/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'os_replacement' => 'BlackBerry OS',
        ],
        [
            'regex' => '@(Black[Bb]erry).+Version/(\\d+)\\.(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'os_replacement' => 'BlackBerry OS',
        ],
        [
            'regex' => '@(RIM Tablet OS) (\\d+)\\.(\\d+)\\.(\\d+)@',
            'os_replacement' => 'BlackBerry Tablet OS',
        ],
        [
            'regex' => '@(Play[Bb]ook)@',
            'os_replacement' => 'BlackBerry Tablet OS',
        ],
        [
            'regex' => '@(Black[Bb]erry)@',
            'os_replacement' => 'BlackBerry OS',
        ],
        [
            'regex' => '@(K[Aa][Ii]OS)\\/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'os_replacement' => 'KaiOS',
        ],
        [
            'regex' => '@\\((?:Mobile|Tablet);.+Gecko/18.0 Firefox/\\d+\\.\\d+@',
            'os_replacement' => 'Firefox OS',
            'os_v1_replacement' => '1',
            'os_v2_replacement' => '0',
            'os_v3_replacement' => '1',
        ],
        [
            'regex' => '@\\((?:Mobile|Tablet);.+Gecko/18.1 Firefox/\\d+\\.\\d+@',
            'os_replacement' => 'Firefox OS',
            'os_v1_replacement' => '1',
            'os_v2_replacement' => '1',
        ],
        [
            'regex' => '@\\((?:Mobile|Tablet);.+Gecko/26.0 Firefox/\\d+\\.\\d+@',
            'os_replacement' => 'Firefox OS',
            'os_v1_replacement' => '1',
            'os_v2_replacement' => '2',
        ],
        [
            'regex' => '@\\((?:Mobile|Tablet);.+Gecko/28.0 Firefox/\\d+\\.\\d+@',
            'os_replacement' => 'Firefox OS',
            'os_v1_replacement' => '1',
            'os_v2_replacement' => '3',
        ],
        [
            'regex' => '@\\((?:Mobile|Tablet);.+Gecko/30.0 Firefox/\\d+\\.\\d+@',
            'os_replacement' => 'Firefox OS',
            'os_v1_replacement' => '1',
            'os_v2_replacement' => '4',
        ],
        [
            'regex' => '@\\((?:Mobile|Tablet);.+Gecko/32.0 Firefox/\\d+\\.\\d+@',
            'os_replacement' => 'Firefox OS',
            'os_v1_replacement' => '2',
            'os_v2_replacement' => '0',
        ],
        [
            'regex' => '@\\((?:Mobile|Tablet);.+Gecko/34.0 Firefox/\\d+\\.\\d+@',
            'os_replacement' => 'Firefox OS',
            'os_v1_replacement' => '2',
            'os_v2_replacement' => '1',
        ],
        [
            'regex' => '@\\((?:Mobile|Tablet);.+Firefox/\\d+\\.\\d+@',
            'os_replacement' => 'Firefox OS',
        ],
        ['regex' => '@(BREW)[ /](\\d+)\\.(\\d+)\\.(\\d+)@'],
        ['regex' => '@(BREW);@'],
        [
            'regex' => '@(Brew MP|BMP)[ /](\\d+)\\.(\\d+)\\.(\\d+)@',
            'os_replacement' => 'Brew MP',
        ],
        [
            'regex' => '@BMP;@',
            'os_replacement' => 'Brew MP',
        ],
        ['regex' => '@(GoogleTV)(?: (\\d+)\\.(\\d+)(?:\\.(\\d+)|)|/[\\da-z]+)@'],
        ['regex' => '@(WebTV)/(\\d+).(\\d+)@'],
        [
            'regex' => '@(CrKey)(?:[/](\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)@',
            'os_replacement' => 'Chromecast',
        ],
        [
            'regex' => '@(hpw|web)OS/(\\d+)\\.(\\d+)(?:\\.(\\d+)|)@',
            'os_replacement' => 'webOS',
        ],
        ['regex' => '@(VRE);@'],
        ['regex' => '@(Fedora|Red Hat|PCLinuxOS|Puppy|Ubuntu|Kindle|Bada|Sailfish|Lubuntu|BackTrack|Slackware|(?:Free|Open|Net|\\b)BSD)[/ ](\\d+)\\.(\\d+)(?:\\.(\\d+)|)(?:\\.(\\d+)|)@'],
        [
            'regex' => '@(Linux)[ /](\\d+)\\.(\\d+)(?:\\.(\\d+)|).*gentoo@',
            'os_replacement' => 'Gentoo',
        ],
        ['regex' => '@\\((Bada);@'],
        ['regex' => '@(Windows|Android|WeTab|Maemo|Web0S)@'],
        ['regex' => '@(Ubuntu|Kubuntu|Arch Linux|CentOS|Slackware|Gentoo|openSUSE|SUSE|Red Hat|Fedora|PCLinuxOS|Mageia|(?:Free|Open|Net|\\b)BSD)@'],
        ['regex' => '@(Linux)(?:[ /](\\d+)\\.(\\d+)(?:\\.(\\d+)|)|)@'],
        [
            'regex' => '@SunOS@',
            'os_replacement' => 'Solaris',
        ],
        [
            'regex' => '@\\(linux-gnu\\)@',
            'os_replacement' => 'Linux',
        ],
        [
            'regex' => '@\\(x86_64-redhat-linux-gnu\\)@',
            'os_replacement' => 'Red Hat',
        ],
        [
            'regex' => '@\\((freebsd)(\\d+)\\.(\\d+)\\)@',
            'os_replacement' => 'FreeBSD',
        ],
        [
            'regex' => '@linux@',
            'os_replacement' => 'Linux',
        ],
        ['regex' => '@^(Roku)/DVP-(\\d+)\\.(\\d+)@'],
    ],
    'device_parsers' => [
        [
            'regex' => '@(?:(?:iPhone|Windows CE|Windows Phone|Android).*(?:(?:Bot|Yeti)-Mobile|YRSpider|BingPreview|bots?/\\d|(?:bot|spider)\\.html)|AdsBot-Google-Mobile.*iPhone)@i',
            'device_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
            'model_replacement' => 'Smartphone',
        ],
        [
            'regex' => '@(?:DoCoMo|\\bMOT\\b|\\bLG\\b|Nokia|Samsung|SonyEricsson).*(?:(?:Bot|Yeti)-Mobile|bots?/\\d|(?:bot|crawler)\\.html|(?:jump|google|Wukong)bot|ichiro/mobile|/spider|YahooSeeker)@i',
            'device_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
            'model_replacement' => 'Feature Phone',
        ],
        [
            'regex' => '@ PTST/\\d+(?:\\.)?\\d+$@',
            'device_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
        ],
        [
            'regex' => '@X11; Datanyze; Linux@',
            'device_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
        ],
        [
            'regex' => '@Mozilla.*Mobile.*AspiegelBot@',
            'device_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
            'model_replacement' => 'Smartphone',
        ],
        [
            'regex' => '@Mozilla.*AspiegelBot@',
            'device_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
            'model_replacement' => 'Desktop',
        ],
        [
            'regex' => '@\\bSmartWatch {0,2}\\( {0,2}([^;]+) {0,2}; {0,2}([^;]+) {0,2};@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@Android Application[^\\-]+ - (Sony) ?(Ericsson|) (.+) \\w+ - @',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1$2',
            'model_replacement' => '$3',
        ],
        [
            'regex' => '@Android Application[^\\-]+ - (?:HTC|HUAWEI|LGE|LENOVO|MEDION|TCT) (HTC|HUAWEI|LG|LENOVO|MEDION|ALCATEL)[ _\\-](.+) \\w+ - @i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@Android Application[^\\-]+ - ([^ ]+) (.+) \\w+ - @',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *([BLRQ]C\\d{4}[A-Z]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '3Q $1',
            'brand_replacement' => '3Q',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:3Q_)([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '3Q $1',
            'brand_replacement' => '3Q',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android [34].*; *(A100|A101|A110|A200|A210|A211|A500|A501|A510|A511|A700(?: Lite| 3G|)|A701|B1-A71|A1-\\d{3}|B1-\\d{3}|V360|V370|W500|W500P|W501|W501P|W510|W511|W700|Slider SL101|DA22[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Acer',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Acer Iconia Tab ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Acer',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Z1[1235]0|E320[^/]*|S500|S510|Liquid[^;/]*|Iconia A\\d+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Acer',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Acer |ACER )([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Acer',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Advent |)(Vega(?:Bean|Comb|)).*?(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Advent',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Ainol |)((?:NOVO|[Nn]ovo)[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Ainol',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *AIRIS[ _\\-]?([^/;\\)]+) *(?:;|\\)|Build)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Airis',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(OnePAD[^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Airis',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Airpad[ \\-]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Airpad $1',
            'brand_replacement' => 'Airpad',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(one ?touch) (EVO7|T10|T20)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Alcatel One Touch $2',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => 'One Touch $2',
        ],
        [
            'regex' => '@; *(?:alcatel[ _]|)(?:(?:one[ _]?touch[ _])|ot[ \\-])([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Alcatel One Touch $1',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => 'One Touch $1',
        ],
        [
            'regex' => '@; *(TCL)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Vodafone Smart II|Optimus_Madrid)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Alcatel $1',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *BASE_Lutea_3(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Alcatel One Touch 998',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => 'One Touch 998',
        ],
        [
            'regex' => '@; *BASE_Varia(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Alcatel One Touch 918D',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => 'One Touch 918D',
        ],
        [
            'regex' => '@; *((?:FINE|Fine)\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Allfine',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(ALLVIEW[ _]?|Allview[ _]?)((?:Speed|SPEED).*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Allview',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(ALLVIEW[ _]?|Allview[ _]?|)(AX1_Shine|AX2_Frenzy)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Allview',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(ALLVIEW[ _]?|Allview[ _]?)([^;/]*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Allview',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(A13-MID)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Allwinner',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Allwinner)[ _\\-]?([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Allwinner',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A651|A701B?|A702|A703|A705|A706|A707|A711|A712|A713|A717|A722|A785|A801|A802|A803|A901|A902|A1002|A1003|A1006|A1007|A9701|A9703|Q710|Q80)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Amaway',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:AMOI|Amoi)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Amoi $1',
            'brand_replacement' => 'Amoi',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@^(?:AMOI|Amoi)[ _]([^;/]+?) Linux@',
            'device_replacement' => 'Amoi $1',
            'brand_replacement' => 'Amoi',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(MW(?:0[789]|10)[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Aoc',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(G7|M1013|M1015G|M11[CG]?|M-?12[B]?|M15|M19[G]?|M30[ACQ]?|M31[GQ]|M32|M33[GQ]|M36|M37|M38|M701T|M710|M712B|M713|M715G|M716G|M71(?:G|GS|T|)|M72[T]?|M73[T]?|M75[GT]?|M77G|M79T|M7L|M7LN|M81|M810|M81T|M82|M92|M92KS|M92S|M717G|M721|M722G|M723|M725G|M739|M785|M791|M92SK|M93D)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Aoson $1',
            'brand_replacement' => 'Aoson',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Aoson ([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Aoson $1',
            'brand_replacement' => 'Aoson',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *[Aa]panda[ _\\-]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Apanda $1',
            'brand_replacement' => 'Apanda',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:ARCHOS|Archos) ?(GAMEPAD.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Archos $1',
            'brand_replacement' => 'Archos',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@ARCHOS; GOGI; ([^;]+);@',
            'device_replacement' => 'Archos $1',
            'brand_replacement' => 'Archos',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:ARCHOS|Archos)[ _]?(.*?)(?: Build|[;/\\(\\)\\-]|$)@',
            'device_replacement' => 'Archos $1',
            'brand_replacement' => 'Archos',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(AN(?:7|8|9|10|13)[A-Z0-9]{1,4})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Archos $1',
            'brand_replacement' => 'Archos',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A28|A32|A43|A70(?:BHT|CHT|HB|S|X)|A101(?:B|C|IT)|A7EB|A7EB-WK|101G9|80G9)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Archos $1',
            'brand_replacement' => 'Archos',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(PAD-FMD[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Arival',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(BioniQ) ?([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Arival',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(AN\\d[^;/]+|ARCHM\\d+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Arnova $1',
            'brand_replacement' => 'Arnova',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:ARNOVA|Arnova) ?([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Arnova $1',
            'brand_replacement' => 'Arnova',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:ASSISTANT |)(AP)-?([1789]\\d{2}[A-Z]{0,2}|80104)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Assistant $1-$2',
            'brand_replacement' => 'Assistant',
            'model_replacement' => '$1-$2',
        ],
        [
            'regex' => '@; *(ME17\\d[^;/]*|ME3\\d{2}[^;/]+|K00[A-Z]|Nexus 10|Nexus 7(?: 2013|)|PadFone[^;/]*|Transformer[^;/]*|TF\\d{3}[^;/]*|eeepc)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Asus $1',
            'brand_replacement' => 'Asus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *ASUS[ _]*([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Asus $1',
            'brand_replacement' => 'Asus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Garmin-Asus ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Garmin-Asus $1',
            'brand_replacement' => 'Garmin-Asus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Garminfone)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Garmin $1',
            'brand_replacement' => 'Garmin-Asus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; (\\@TAB-[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Attab',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(T-(?:07|[^0]\\d)[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Audiosonic',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Axioo[ _\\-]([^;/]+?)|(picopad)[ _\\-]([^;/]+?))(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Axioo $1$2 $3',
            'brand_replacement' => 'Axioo',
            'model_replacement' => '$1$2 $3',
        ],
        [
            'regex' => '@; *(V(?:100|700|800)[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Azend',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IBAK\\-[^;/]*)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Bak',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(HY5001|HY6501|X12|X21|I5)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Bedove $1',
            'brand_replacement' => 'Bedove',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(JC-[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Benss $1',
            'brand_replacement' => 'Benss',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(BB) ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Blackberry',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(BlackBird)[ _](I8.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(BlackBird)[ _](.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *([0-9]+BP[EM][^;/]*|Endeavour[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Blaupunkt $1',
            'brand_replacement' => 'Blaupunkt',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:BLU|Blu)[ _\\-])([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Blu',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(?:BMOBILE )?(Blu|BLU|DASH [^;/]+|VIVO 4\\.3|TANK 4\\.5)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Blu',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TOUCH\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Blusens',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(AX5\\d+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Bmobile',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([Bb]q) ([^;/]+?);?(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'bq',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Maxwell [^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'bq',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:B-Tab|B-TAB) ?\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Braun',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Broncho) ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *CAPTIVA ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Captiva $1',
            'brand_replacement' => 'Captiva',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(C771|CAL21|IS11CA)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Casio',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Cat|CAT) ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Cat $1',
            'brand_replacement' => 'Cat',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Cat)(Nova.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Cat $1',
            'brand_replacement' => 'Cat',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(INM8002KP|ADM8000KP_[AB])(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Cat',
            'model_replacement' => 'Tablet PHOENIX 8.1J0',
        ],
        [
            'regex' => '@; *(?:[Cc]elkon[ _\\*]|CELKON[ _\\*])([^;/\\)]+) ?(?:Build|;|\\))@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Celkon',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Build/(?:[Cc]elkon)+_?([^;/_\\)]+)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Celkon',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(CT)-?(\\d+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Celkon',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *(A19|A19Q|A105|A107[^;/\\)]*) ?(?:Build|;|\\))@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Celkon',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TPC[0-9]{4,5})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'ChangJia',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Cloudfone)[ _](Excite)([^ ][^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2 $3',
            'brand_replacement' => 'Cloudfone',
            'model_replacement' => '$1 $2 $3',
        ],
        [
            'regex' => '@; *(Excite|ICE)[ _](\\d+[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Cloudfone $1 $2',
            'brand_replacement' => 'Cloudfone',
            'model_replacement' => 'Cloudfone $1 $2',
        ],
        [
            'regex' => '@; *(Cloudfone|CloudPad)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Cloudfone',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *((?:Aquila|Clanga|Rapax)[^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Cmx',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:CFW-|Kyros )?(MID[0-9]{4}(?:[ABC]|SR|TV)?)(\\(3G\\)-4G| GB 8K| 3G| 8K| GB)? *(?:Build|[;\\)])@',
            'device_replacement' => 'CobyKyros $1$2',
            'brand_replacement' => 'CobyKyros',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *([^;/]*)Coolpad[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Coolpad',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *(CUBE[ _])?([KU][0-9]+ ?GT.*?|A5300)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Cube',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *CUBOT ([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Cubot',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(BOBBY)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Cubot',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Dslide [^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Danew',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(XCD)[ _]?(28|35)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1$2',
            'brand_replacement' => 'Dell',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *(001DL)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => 'Streak',
        ],
        [
            'regex' => '@; *(?:Dell|DELL) (Streak)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => 'Streak',
        ],
        [
            'regex' => '@; *(101DL|GS01|Streak Pro[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => 'Streak Pro',
        ],
        [
            'regex' => '@; *([Ss]treak ?7)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => 'Streak 7',
        ],
        [
            'regex' => '@; *(Mini-3iX)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Dell|DELL)[ _](Aero|Venue|Thunder|Mini.*?|Streak[ _]Pro)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Dell[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Dell ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TA[CD]-\\d+[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Denver',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(iP[789]\\d{2}(?:-3G)?|IP10\\d{2}(?:-8GB)?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Dex',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(AirTab)[ _\\-]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'DNS',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(F\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Fujitsu',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(HT-03A)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => 'Magic',
        ],
        [
            'regex' => '@; *(HT\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(L\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(N\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Nec',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(P\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Panasonic',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SC\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SH\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sharp',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SO\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'SonyEricsson',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(T\\-0[12][^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Toshiba',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(DOOV)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'DOOV',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Enot|ENOT)[ -]?([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Enot',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *[^;/]+ Build/(?:CROSS|Cross)+[ _\\-]([^\\)]+)@',
            'device_replacement' => 'CROSS $1',
            'brand_replacement' => 'Evercoss',
            'model_replacement' => 'Cross $1',
        ],
        [
            'regex' => '@; *(CROSS|Cross)[ _\\-]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Evercoss',
            'model_replacement' => 'Cross $2',
        ],
        [
            'regex' => '@; *Explay[_ ](.+?)(?:[\\)]| Build)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Explay',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IQ.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Fly',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Fly|FLY)[ _](IQ[^;]+?|F[34]\\d+[^;]*?);?(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Fly',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(M532|Q572|FJL21)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Fujitsu',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(G1)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Galapad',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Geeksphone) ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(G[^F]?FIVE) ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Gfive',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Gionee)[ _\\-]([^;/]+?)(?:/[^;/]+|)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Gionee',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(GN\\d+[A-Z]?|INFINITY_PASSION|Ctrl_V1)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Gionee $1',
            'brand_replacement' => 'Gionee',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(E3) Build/JOP40D@',
            'device_replacement' => 'Gionee $1',
            'brand_replacement' => 'Gionee',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\sGIONEE[-\\s_](\\w*)@i',
            'device_replacement' => 'Gionee $1',
            'brand_replacement' => 'Gionee',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:FONE|QUANTUM|INSIGNIA) \\d+[^;/]*|PLAYTAB)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'GoClever $1',
            'brand_replacement' => 'GoClever',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *GOCLEVER ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'GoClever $1',
            'brand_replacement' => 'GoClever',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Glass \\d+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Google',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Pixel.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Google',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(GSmart)[ -]([^/]+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Gigabyte',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(imx5[13]_[^/]+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Freescale $1',
            'brand_replacement' => 'Freescale',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Haier[ _\\-]([^/]+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Haier $1',
            'brand_replacement' => 'Haier',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(PAD1016)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Haipad $1',
            'brand_replacement' => 'Haipad',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(M701|M7|M8|M9)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Haipad $1',
            'brand_replacement' => 'Haipad',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SN\\d+T[^;\\)/]*)(?: Build|[;\\)])@',
            'device_replacement' => 'Hannspree $1',
            'brand_replacement' => 'Hannspree',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Build/HCL ME Tablet ([^;\\)]+)[\\);]@',
            'device_replacement' => 'HCLme $1',
            'brand_replacement' => 'HCLme',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([^;\\/]+) Build/HCL@',
            'device_replacement' => 'HCLme $1',
            'brand_replacement' => 'HCLme',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(MID-?\\d{4}C[EM])(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Hena $1',
            'brand_replacement' => 'Hena',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(EG\\d{2,}|HS-[^;/]+|MIRA[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Hisense $1',
            'brand_replacement' => 'Hisense',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(andromax[^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Hisense $1',
            'brand_replacement' => 'Hisense',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:AMAZE[ _](S\\d+)|(S\\d+)[ _]AMAZE)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'AMAZE $1$2',
            'brand_replacement' => 'hitech',
            'model_replacement' => 'AMAZE $1$2',
        ],
        [
            'regex' => '@; *(PlayBook)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'HP $1',
            'brand_replacement' => 'HP',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *HP ([^/]+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'HP $1',
            'brand_replacement' => 'HP',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([^/]+_tenderloin)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'HP TouchPad',
            'brand_replacement' => 'HP',
            'model_replacement' => 'TouchPad',
        ],
        [
            'regex' => '@; *(HUAWEI |Huawei-|)([UY][^;/]+) Build/(?:Huawei|HUAWEI)([UY][^\\);]+)\\)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *([^;/]+) Build[/ ]Huawei(MT1-U06|[A-Z]+\\d+[^\\);]+)\\)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(S7|M860) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:HUAWEI|Huawei)[ \\-]?)(MediaPad) Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *((?:HUAWEI[ _]?|Huawei[ _]|)Ascend[ _])([^;/]+) Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *((?:HUAWEI|Huawei)[ _\\-]?)((?:G700-|MT-)[^;/]+) Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *((?:HUAWEI|Huawei)[ _\\-]?)([^;/]+) Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(MediaPad[^;]+|SpringBoard) Build/Huawei@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([^;]+) Build/(?:Huawei|HUAWEI)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([Uu])([89]\\d{3}) Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Huawei',
            'model_replacement' => 'U$2',
        ],
        [
            'regex' => '@; *(?:Ideos |IDEOS )(S7) Build@',
            'device_replacement' => 'Huawei Ideos$1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => 'Ideos$1',
        ],
        [
            'regex' => '@; *(?:Ideos |IDEOS )([^;/]+\\s*|\\s*)Build@',
            'device_replacement' => 'Huawei Ideos$1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => 'Ideos$1',
        ],
        [
            'regex' => '@; *(Orange Daytona|Pulse|Pulse Mini|Vodafone 858|C8500|C8600|C8650|C8660|Nexus 6P|ATH-.+?) Build[/ ]@',
            'device_replacement' => 'Huawei $1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:[A-Z]{3})\\-L[A-Za0-9]{2})[\\)]@',
            'device_replacement' => 'Huawei $1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *HTC[ _]([^;]+); Windows Phone@',
            'device_replacement' => 'HTC $1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:HTC[ _/])+([^ _/]+)(?:[/\\\\]1\\.0 | V|/| +)\\d+\\.\\d[\\d\\.]*(?: *Build|\\))@',
            'device_replacement' => 'HTC $1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:HTC[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)|)(?:[/\\\\]1\\.0 | V|/| +)\\d+\\.\\d[\\d\\.]*(?: *Build|\\))@',
            'device_replacement' => 'HTC $1 $2',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(?:HTC[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/]+)|)|)(?:[/\\\\]1\\.0 | V|/| +)\\d+\\.\\d[\\d\\.]*(?: *Build|\\))@',
            'device_replacement' => 'HTC $1 $2 $3',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1 $2 $3',
        ],
        [
            'regex' => '@; *(?:HTC[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/]+)|)|)|)(?:[/\\\\]1\\.0 | V|/| +)\\d+\\.\\d[\\d\\.]*(?: *Build|\\))@',
            'device_replacement' => 'HTC $1 $2 $3 $4',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1 $2 $3 $4',
        ],
        [
            'regex' => '@; *(?:(?:HTC|htc)(?:_blocked|)[ _/])+([^ _/;]+)(?: *Build|[;\\)]| - )@',
            'device_replacement' => 'HTC $1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:(?:HTC|htc)(?:_blocked|)[ _/])+([^ _/]+)(?:[ _/]([^ _/;\\)]+)|)(?: *Build|[;\\)]| - )@',
            'device_replacement' => 'HTC $1 $2',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(?:(?:HTC|htc)(?:_blocked|)[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/;\\)]+)|)|)(?: *Build|[;\\)]| - )@',
            'device_replacement' => 'HTC $1 $2 $3',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1 $2 $3',
        ],
        [
            'regex' => '@; *(?:(?:HTC|htc)(?:_blocked|)[ _/])+([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ _/]+)(?:[ _/]([^ /;]+)|)|)|)(?: *Build|[;\\)]| - )@',
            'device_replacement' => 'HTC $1 $2 $3 $4',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1 $2 $3 $4',
        ],
        [
            'regex' => '@HTC Streaming Player [^\\/]*/[^\\/]*/ htc_([^/]+) /@',
            'device_replacement' => 'HTC $1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:[;,] *|^)(?:htccn_chs-|)HTC[ _-]?([^;]+?)(?: *Build|clay|Android|-?Mozilla| Opera| Profile| UNTRUSTED|[;/\\(\\)]|$)@i',
            'device_replacement' => 'HTC $1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A6277|ADR6200|ADR6300|ADR6350|ADR6400[A-Z]*|ADR6425[A-Z]*|APX515CKT|ARIA|Desire[^_ ]*|Dream|EndeavorU|Eris|Evo|Flyer|HD2|Hero|HERO200|Hero CDMA|HTL21|Incredible|Inspire[A-Z0-9]*|Legend|Liberty|Nexus ?(?:One|HD2)|One|One S C2|One[ _]?(?:S|V|X\\+?)\\w*|PC36100|PG06100|PG86100|S31HT|Sensation|Wildfire)(?: Build|[/;\\(\\)])@i',
            'device_replacement' => 'HTC $1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(ADR6200|ADR6400L|ADR6425LVW|Amaze|DesireS?|EndeavorU|Eris|EVO|Evo\\d[A-Z]+|HD2|IncredibleS?|Inspire[A-Z0-9]*|Inspire[A-Z0-9]*|Sensation[A-Z0-9]*|Wildfire)[ _-](.+?)(?:[/;\\)]|Build|MIUI|1\\.0)@i',
            'device_replacement' => 'HTC $1 $2',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *HYUNDAI (T\\d[^/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Hyundai $1',
            'brand_replacement' => 'Hyundai',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *HYUNDAI ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Hyundai $1',
            'brand_replacement' => 'Hyundai',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(X700|Hold X|MB-6900)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Hyundai $1',
            'brand_replacement' => 'Hyundai',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:iBall[ _\\-]|)(Andi)[ _]?(\\d[^;/]*)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'iBall',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(IBall)(?:[ _]([^;/]+?)|)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'iBall',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(NT-\\d+[^ ;/]*|Net[Tt]AB [^;/]+|Mercury [A-Z]+|iconBIT)(?: S/N:[^;/]+|)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'IconBIT',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IMO)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'IMO',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *i-?mobile[ _]([^/]+)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'i-mobile $1',
            'brand_replacement' => 'imobile',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(i-(?:style|note)[^/]*)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'i-mobile $1',
            'brand_replacement' => 'imobile',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(ImPAD) ?(\\d+(?:.)*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Impression',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(Infinix)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Infinix',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Informer)[ \\-]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Informer',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(TAB) ?([78][12]4)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Intenso $1',
            'brand_replacement' => 'Intenso',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(?:Intex[ _]|)(AQUA|Aqua)([ _\\.\\-])([^;/]+?) *(?:Build|;)@',
            'device_replacement' => '$1$2$3',
            'brand_replacement' => 'Intex',
            'model_replacement' => '$1 $3',
        ],
        [
            'regex' => '@; *(?:INTEX|Intex)(?:[_ ]([^\\ _;/]+))(?:[_ ]([^\\ _;/]+)|) *(?:Build|;)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Intex',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *([iI]Buddy)[ _]?(Connect)(?:_|\\?_| |)([^;/]*) *(?:Build|;)@',
            'device_replacement' => '$1 $2 $3',
            'brand_replacement' => 'Intex',
            'model_replacement' => 'iBuddy $2 $3',
        ],
        [
            'regex' => '@; *(I-Buddy)[ _]([^;/]+?) *(?:Build|;)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Intex',
            'model_replacement' => 'iBuddy $2',
        ],
        [
            'regex' => '@; *(iOCEAN) ([^/]+)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'iOCEAN',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(TP\\d+(?:\\.\\d+|)\\-\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'ionik $1',
            'brand_replacement' => 'ionik',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(M702pro)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Iru',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *itel ([^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Itel $1',
            'brand_replacement' => 'Itel',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(DE88Plus|MD70)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Ivio',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *IVIO[_\\-]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Ivio',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TPC-\\d+|JAY-TECH)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Jaytech',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(JY-[^;/]+|G[234]S?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Jiayu',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(JXD)[ _\\-]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'JXD',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *Karbonn[ _]?([^;/]+) *(?:Build|;)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Karbonn',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([^;]+) Build/Karbonn@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Karbonn',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A11|A39|A37|A34|ST8|ST10|ST7|Smart Tab3|Smart Tab2|Titanium S\\d) +Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Karbonn',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IS01|IS03|IS05|IS\\d{2}SH)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sharp',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IS04)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Regza',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IS06|IS\\d{2}PT)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Pantech',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IS11S)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'SonyEricsson',
            'model_replacement' => 'Xperia Acro',
        ],
        [
            'regex' => '@; *(IS11CA)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Casio',
            'model_replacement' => 'GzOne $1',
        ],
        [
            'regex' => '@; *(IS11LG)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'LG',
            'model_replacement' => 'Optimus X',
        ],
        [
            'regex' => '@; *(IS11N)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Medias',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IS11PT)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Pantech',
            'model_replacement' => 'MIRACH',
        ],
        [
            'regex' => '@; *(IS12F)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Fujitsu',
            'model_replacement' => 'Arrows ES',
        ],
        [
            'regex' => '@; *(IS12M)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => 'XT909',
        ],
        [
            'regex' => '@; *(IS12S)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'SonyEricsson',
            'model_replacement' => 'Xperia Acro HD',
        ],
        [
            'regex' => '@; *(ISW11F)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Fujitsu',
            'model_replacement' => 'Arrowz Z',
        ],
        [
            'regex' => '@; *(ISW11HT)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => 'EVO',
        ],
        [
            'regex' => '@; *(ISW11K)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Kyocera',
            'model_replacement' => 'DIGNO',
        ],
        [
            'regex' => '@; *(ISW11M)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => 'Photon',
        ],
        [
            'regex' => '@; *(ISW11SC)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => 'GALAXY S II WiMAX',
        ],
        [
            'regex' => '@; *(ISW12HT)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => 'EVO 3D',
        ],
        [
            'regex' => '@; *(ISW13HT)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => 'J',
        ],
        [
            'regex' => '@; *(ISW?[0-9]{2}[A-Z]{0,2})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'KDDI',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(INFOBAR [^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'KDDI',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(JOYPAD|Joypad)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Kingcom',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(Vox|VOX|Arc|K080)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Kobo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\b(Kobo Touch)\\b@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Kobo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(K-Touch)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Ktouch',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *((?:EV|KM)-S\\d+[A-Z]?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'KTtech',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Zio|Hydro|Torque|Event|EVENT|Echo|Milano|Rise|URBANO PROGRESSO|WX04K|WX06K|WX10K|KYL21|101K|C5[12]\\d{2})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Kyocera',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:LAVA[ _]|)IRIS[ _\\-]?([^/;\\)]+) *(?:;|\\)|Build)@i',
            'device_replacement' => 'Iris $1',
            'brand_replacement' => 'Lava',
            'model_replacement' => 'Iris $1',
        ],
        [
            'regex' => '@; *LAVA[ _]([^;/]+) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Lava',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:(Aspire A1)|(?:LEMON|Lemon)[ _]([^;/]+))_?(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Lemon $1$2',
            'brand_replacement' => 'Lemon',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *(TAB-1012)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Lenco $1',
            'brand_replacement' => 'Lenco',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; Lenco ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Lenco $1',
            'brand_replacement' => 'Lenco',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A1_07|A2107A-H|S2005A-H|S1-37AH0) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Idea[Tp]ab)[ _]([^;/]+);? Build@',
            'device_replacement' => 'Lenovo $1 $2',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(Idea(?:Tab|pad)) ?([^;/]+) Build@',
            'device_replacement' => 'Lenovo $1 $2',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(ThinkPad) ?(Tablet) Build/@',
            'device_replacement' => 'Lenovo $1 $2',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(?:LNV-|)(?:=?[Ll]enovo[ _\\-]?|LENOVO[ _])(.+?)(?:Build|[;/\\)])@',
            'device_replacement' => 'Lenovo $1',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@[;,] (?:Vodafone |)(SmartTab) ?(II) ?(\\d+) Build/@',
            'device_replacement' => 'Lenovo $1 $2 $3',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1 $2 $3',
        ],
        [
            'regex' => '@; *(?:Ideapad |)K1 Build/@',
            'device_replacement' => 'Lenovo Ideapad K1',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => 'Ideapad K1',
        ],
        [
            'regex' => '@; *(3GC101|3GW10[01]|A390) Build/@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\b(?:Lenovo|LENOVO)+[ _\\-]?([^,;:/ ]+)@',
            'device_replacement' => 'Lenovo $1',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(MFC\\d+)[A-Z]{2}([^;,/]*),?(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Lexibook',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *(E[34][0-9]{2}|LS[6-8][0-9]{2}|VS[6-9][0-9]+[^;/]+|Nexus 4|Nexus 5X?|GT540f?|Optimus (?:2X|G|4X HD)|OptimusX4HD) *(?:Build|;)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@[;:] *(L-\\d+[A-Z]|LGL\\d+[A-Z]?)(?:/V\\d+|) *(?:Build|[;\\)])@',
            'device_replacement' => '$1',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(LG-)([A-Z]{1,2}\\d{2,}[^,;/\\)\\(]*?)(?:Build| V\\d+|[,;/\\)\\(]|$)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'LG',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(LG[ \\-]|LG)([^;/]+)[;/]? Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'LG',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@^(LG)-([^;/]+)/ Mozilla/.*; Android@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'LG',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@(Web0S); Linux/(SmartTV)@',
            'device_replacement' => 'LG $1 $2',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *((?:SMB|smb)[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Malata',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Malata|MALATA) ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Malata',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(MS[45][0-9]{3}|MID0[568][NS]?|MID[1-9]|MID[78]0[1-9]|MID970[1-9]|MID100[1-9])(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Manta',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(M1052|M806|M9000|M9100|M9701|MID100|MID120|MID125|MID130|MID135|MID140|MID701|MID710|MID713|MID727|MID728|MID731|MID732|MID733|MID735|MID736|MID737|MID760|MID800|MID810|MID820|MID830|MID833|MID835|MID860|MID900|MID930|MID933|MID960|MID980)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Match',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(GenxDroid7|MSD7.*?|AX\\d.*?|Tab 701|Tab 722)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Maxx $1',
            'brand_replacement' => 'Maxx',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(M-PP[^;/]+|PhonePad ?\\d{2,}[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Mediacom $1',
            'brand_replacement' => 'Mediacom',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(M-MP[^;/]+|SmartPad ?\\d{2,}[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Mediacom $1',
            'brand_replacement' => 'Mediacom',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:MD_|)LIFETAB[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Medion Lifetab $1',
            'brand_replacement' => 'Medion',
            'model_replacement' => 'Lifetab $1',
        ],
        [
            'regex' => '@; *MEDION ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Medion $1',
            'brand_replacement' => 'Medion',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(M030|M031|M035|M040|M065|m9)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Meizu $1',
            'brand_replacement' => 'Meizu',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:meizu_|MEIZU )(.+?) *(?:Build|[;\\)])@',
            'device_replacement' => 'Meizu $1',
            'brand_replacement' => 'Meizu',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Micromax[ _](A111|A240)|(A111|A240)) Build@i',
            'device_replacement' => 'Micromax $1$2',
            'brand_replacement' => 'Micromax',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *Micromax[ _](A\\d{2,3}[^;/]*) Build@i',
            'device_replacement' => 'Micromax $1',
            'brand_replacement' => 'Micromax',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A\\d{2}|A[12]\\d{2}|A90S|A110Q) Build@i',
            'device_replacement' => 'Micromax $1',
            'brand_replacement' => 'Micromax',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Micromax[ _](P\\d{3}[^;/]*) Build@i',
            'device_replacement' => 'Micromax $1',
            'brand_replacement' => 'Micromax',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(P\\d{3}|P\\d{3}\\(Funbook\\)) Build@i',
            'device_replacement' => 'Micromax $1',
            'brand_replacement' => 'Micromax',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(MITO)[ _\\-]?([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Mito',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Cynus)[ _](F5|T\\d|.+?) *(?:Build|[;/\\)])@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Mobistel',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(MODECOM |)(FreeTab) ?([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1$2 $3',
            'brand_replacement' => 'Modecom',
            'model_replacement' => '$2 $3',
        ],
        [
            'regex' => '@; *(MODECOM )([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Modecom',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(MZ\\d{3}\\+?|MZ\\d{3} 4G|Xoom|XOOM[^;/]*) Build@',
            'device_replacement' => 'Motorola $1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Milestone )(XT[^;/]*) Build@',
            'device_replacement' => 'Motorola $1$2',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Motoroi ?x|Droid X|DROIDX) Build@i',
            'device_replacement' => 'Motorola $1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => 'DROID X',
        ],
        [
            'regex' => '@; *(Droid[^;/]*|DROID[^;/]*|Milestone[^;/]*|Photon|Triumph|Devour|Titanium) Build@',
            'device_replacement' => 'Motorola $1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A555|A85[34][^;/]*|A95[356]|ME[58]\\d{2}\\+?|ME600|ME632|ME722|MB\\d{3}\\+?|MT680|MT710|MT870|MT887|MT917|WX435|WX453|WX44[25]|XT\\d{3,4}[A-Z\\+]*|CL[iI]Q|CL[iI]Q XT) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Motorola MOT-|Motorola[ _\\-]|MOT\\-?)([^;/]+) Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Moto[_ ]?|MOT\\-)([^;/]+) Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *((?:MP[DQ]C|MPG\\d{1,4}|MP\\d{3,4}|MID(?:(?:10[234]|114|43|7[247]|8[24]|7)C|8[01]1))[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Mpman',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:MSI[ _]|)(Primo\\d+|Enjoy[ _\\-][^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'Msi',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Multilaser[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Multilaser',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(My)[_]?(Pad)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2 $3',
            'brand_replacement' => 'MyPhone',
            'model_replacement' => '$1$2 $3',
        ],
        [
            'regex' => '@; *(My)\\|?(Phone)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2 $3',
            'brand_replacement' => 'MyPhone',
            'model_replacement' => '$3',
        ],
        [
            'regex' => '@; *(A\\d+)[ _](Duo|)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'MyPhone',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(myTab[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Mytab',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(NABI2?-)([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Nabi',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(N-\\d+[CDE])(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Nec',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; ?(NEC-)(.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Nec',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(LT-NA7)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Nec',
            'model_replacement' => 'Lifetouch Note',
        ],
        [
            'regex' => '@; *(NXM\\d+[A-Za-z0-9_]*|Next\\d[A-Za-z0-9_ \\-]*|NEXT\\d[A-Za-z0-9_ \\-]*|Nextbook [A-Za-z0-9_ ]*|DATAM803HC|M805)(?: Build|[\\);])@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Nextbook',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Nokia)([ _\\-]*)([^;/]*) Build@i',
            'device_replacement' => '$1$2$3',
            'brand_replacement' => 'Nokia',
            'model_replacement' => '$3',
        ],
        [
            'regex' => '@; *(TA\\-\\d{4})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Nokia $1',
            'brand_replacement' => 'Nokia',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Nook ?|Barnes & Noble Nook |BN )([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Nook',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(NOOK |)(BNRV200|BNRV200A|BNTV250|BNTV250A|BNTV400|BNTV600|LogicPD Zoom2)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Nook',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; Build/(Nook)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Nook',
            'model_replacement' => 'Tablet',
        ],
        [
            'regex' => '@; *(OP110|OliPad[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Olivetti $1',
            'brand_replacement' => 'Olivetti',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *OMEGA[ _\\-](MID[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Omega $1',
            'brand_replacement' => 'Omega',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@^(MID7500|MID\\d+) Mozilla/5\\.0 \\(iPad;@',
            'device_replacement' => 'Omega $1',
            'brand_replacement' => 'Omega',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:CIUS|cius)[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Openpeak $1',
            'brand_replacement' => 'Openpeak',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Find ?(?:5|7a)|R8[012]\\d{1,2}|T703\\d?|U70\\d{1,2}T?|X90\\d{1,2}|[AFR]\\d{1,2}[a-z]{1,2})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Oppo $1',
            'brand_replacement' => 'Oppo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *OPPO ?([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Oppo $1',
            'brand_replacement' => 'Oppo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(CPH\\d{1,4}|RMX\\d{1,4}|P[A-Z]{3}\\d{2})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Oppo $1',
            'brand_replacement' => 'Oppo',
        ],
        [
            'regex' => '@; *(A1601)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Oppo F1s',
            'brand_replacement' => 'Oppo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Odys\\-|ODYS\\-|ODYS )([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Odys $1',
            'brand_replacement' => 'Odys',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SELECT) ?(7)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Odys $1 $2',
            'brand_replacement' => 'Odys',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(PEDI)_(PLUS)_(W)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Odys $1 $2 $3',
            'brand_replacement' => 'Odys',
            'model_replacement' => '$1 $2 $3',
        ],
        [
            'regex' => '@; *(AEON|BRAVIO|FUSION|FUSION2IN1|Genio|EOS10|IEOS[^;/]*|IRON|Loox|LOOX|LOOX Plus|Motion|NOON|NOON_PRO|NEXT|OPOS|PEDI[^;/]*|PRIME[^;/]*|STUDYTAB|TABLO|Tablet-PC-4|UNO_X8|XELIO[^;/]*|Xelio ?\\d+ ?[Pp]ro|XENO10|XPRESS PRO)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Odys $1',
            'brand_replacement' => 'Odys',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; (ONE [a-zA-Z]\\d+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'OnePlus $1',
            'brand_replacement' => 'OnePlus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; (ONEPLUS [a-zA-Z]\\d+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'OnePlus $1',
            'brand_replacement' => 'OnePlus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TP-\\d+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Orion $1',
            'brand_replacement' => 'Orion',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(G100W?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'PackardBell $1',
            'brand_replacement' => 'PackardBell',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Panasonic)[_ ]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(FZ-A1B|JT-B1)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Panasonic $1',
            'brand_replacement' => 'Panasonic',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(dL1|DL1)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Panasonic $1',
            'brand_replacement' => 'Panasonic',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SKY[ _]|)(IM\\-[AT]\\d{3}[^;/]+).* Build/@',
            'device_replacement' => 'Pantech $1$2',
            'brand_replacement' => 'Pantech',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *((?:ADR8995|ADR910L|ADR930L|ADR930VW|PTL21|P8000)(?: 4G|)) Build/@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Pantech',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Pantech([^;/]+).* Build/@',
            'device_replacement' => 'Pantech $1',
            'brand_replacement' => 'Pantech',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(papyre)[ _\\-]([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Papyre',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(?:Touchlet )?(X10\\.[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Pearl $1',
            'brand_replacement' => 'Pearl',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; PHICOMM (i800)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Phicomm $1',
            'brand_replacement' => 'Phicomm',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; PHICOMM ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Phicomm $1',
            'brand_replacement' => 'Phicomm',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(FWS\\d{3}[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Phicomm $1',
            'brand_replacement' => 'Phicomm',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(D633|D822|D833|T539|T939|V726|W335|W336|W337|W3568|W536|W5510|W626|W632|W6350|W6360|W6500|W732|W736|W737|W7376|W820|W832|W8355|W8500|W8510|W930)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Philips',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Philips|PHILIPS)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Philips $1',
            'brand_replacement' => 'Philips',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android 4\\..*; *(M[12356789]|U[12368]|S[123])\\ ?(pro)?(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Pipo $1$2',
            'brand_replacement' => 'Pipo',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *(MOMO[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Ployer',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Polaroid[ _]|)((?:MIDC\\d{3,}|PMID\\d{2,}|PTAB\\d{3,})[^;/]*?)(\\/[^;/]*|)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Polaroid',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Polaroid )(Tablet)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Polaroid',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(POMP)[ _\\-](.+?) *(?:Build|[;/\\)])@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Pomp',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(TB07STA|TB10STA|TB07FTA|TB10FTA)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Positivo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Positivo |)((?:YPY|Ypy)[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Positivo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(MOB-[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'POV',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *POV[ _\\-]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'POV $1',
            'brand_replacement' => 'POV',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:TAB-PLAYTAB|TAB-PROTAB|PROTAB|PlayTabPro|Mobii[ _\\-]|TAB-P)[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'POV $1',
            'brand_replacement' => 'POV',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Prestigio |)((?:PAP|PMP)\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Prestigio $1',
            'brand_replacement' => 'Prestigio',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(PLT[0-9]{4}.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Proscan',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A2|A5|A8|A900)_?(Classic|)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Qmobile',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; *(Q[Mm]obile)_([^_]+)_([^_]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Qmobile $2 $3',
            'brand_replacement' => 'Qmobile',
            'model_replacement' => '$2 $3',
        ],
        [
            'regex' => '@; *(Q\\-?[Mm]obile)[_ ](A[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Qmobile $2',
            'brand_replacement' => 'Qmobile',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Q\\-Smart)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Qmobilevn',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Q\\-?[Mm]obile)[ _\\-](S[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Qmobilevn',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(TA1013)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Quanta',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; (RCT\\w+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'RCA',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; RCA (\\w+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'RCA $1',
            'brand_replacement' => 'RCA',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(RK\\d+),?(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Rockchip',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@ Build/(RK\\d+)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Rockchip',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SAMSUNG |Samsung |)((?:Galaxy (?:Note II|S\\d)|GT-I9082|GT-I9205|GT-N7\\d{3}|SM-N9005)[^;/]*)\\/?[^;/]* Build/@',
            'device_replacement' => 'Samsung $1$2',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Google |)(Nexus [Ss](?: 4G|)) Build/@',
            'device_replacement' => 'Samsung $1$2',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(SAMSUNG |Samsung )([^\\/]*)\\/[^ ]* Build/@',
            'device_replacement' => 'Samsung $2',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Galaxy(?: Ace| Nexus| S ?II+|Nexus S| with MCR 1.2| Mini Plus 4G|)) Build/@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SAMSUNG[ _\\-]|)(?:SAMSUNG[ _\\-])([^;/]+) Build@',
            'device_replacement' => 'Samsung $2',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(SAMSUNG-|)(GT\\-[BINPS]\\d{4}[^\\/]*)(\\/[^ ]*) Build@',
            'device_replacement' => 'Samsung $1$2$3',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@(?:; *|^)((?:GT\\-[BIiNPS]\\d{4}|I9\\d{2}0[A-Za-z\\+]?\\b)[^;/\\)]*?)(?:Build|Linux|MIUI|[;/\\)])@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; (SAMSUNG-)([A-Za-z0-9\\-]+).* Build/@',
            'device_replacement' => 'Samsung $1$2',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *((?:SCH|SGH|SHV|SHW|SPH|SC|SM)\\-[A-Za-z0-9 ]+)(/?[^ ]*|) Build@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:SC)\\-[A-Za-z0-9 ]+)(/?[^ ]*|)\\)@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@ ((?:SCH)\\-[A-Za-z0-9 ]+)(/?[^ ]*|) Build@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Behold ?(?:2|II)|YP\\-G[^;/]+|EK-GC100|SCL21|I9300) Build@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:SCH|SGH|SHV|SHW|SPH|SC|SM)\\-[A-Za-z0-9]{5,6})[\\)]@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SH\\-?\\d\\d[^;/]+|SBM\\d[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sharp',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SHARP[ -])([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Sharp',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(SPX[_\\-]\\d[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Simvalley',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SX7\\-PEARL\\.GmbH)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Simvalley',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SP[T]?\\-\\d{2}[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Simvalley',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SK\\-.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'SKtelesys',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:SKYTEX|SX)-([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Skytex',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(IMAGINE [^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Skytex',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SmartQ) ?([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(WF7C|WF10C|SBT[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Smartbitt',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SBM(?:003SH|005SH|006SH|007SH|102SH)) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sharp',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(003P|101P|101P11C|102P) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Panasonic',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(00\\dZ) Build/@',
            'device_replacement' => '$1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; HTC(X06HT) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(001HT|X06HT) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(201M) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => 'XT902',
        ],
        [
            'regex' => '@; *(ST\\d{4}.*)Build/ST@',
            'device_replacement' => 'Trekstor $1',
            'brand_replacement' => 'Trekstor',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(ST\\d{4}.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Trekstor $1',
            'brand_replacement' => 'Trekstor',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Sony ?Ericsson ?)([^;/]+) Build@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'SonyEricsson',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *((?:SK|ST|E|X|LT|MK|MT|WT)\\d{2}[a-z0-9]*(?:-o|)|R800i|U20i) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'SonyEricsson',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Xperia (?:A8|Arc|Acro|Active|Live with Walkman|Mini|Neo|Play|Pro|Ray|X\\d+)[^;/]*) Build@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'SonyEricsson',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; Sony (Tablet[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Sony $1',
            'brand_replacement' => 'Sony',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; Sony ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Sony $1',
            'brand_replacement' => 'Sony',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(Sony)([A-Za-z0-9\\-]+)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Xperia [^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sony',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(C(?:1[0-9]|2[0-9]|53|55|6[0-9])[0-9]{2}|D[25]\\d{3}|D6[56]\\d{2})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sony',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SGP\\d{3}|SGPT\\d{2})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sony',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(NW-Z1000Series)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sony',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@PLAYSTATION 3@',
            'device_replacement' => 'PlayStation 3',
            'brand_replacement' => 'Sony',
            'model_replacement' => 'PlayStation 3',
        ],
        [
            'regex' => '@(PlayStation (?:Portable|Vita|\\d+))@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sony',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:CSL_Spice|Spice|SPICE|CSL)[ _\\-]?|)([Mm][Ii])([ _\\-]|)(\\d{3}[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2$3$4',
            'brand_replacement' => 'Spice',
            'model_replacement' => 'Mi$4',
        ],
        [
            'regex' => '@; *(Sprint )(.+?) *(?:Build|[;/])@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Sprint',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@\\b(Sprint)[: ]([^;,/ ]+)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Sprint',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(TAGI[ ]?)(MID) ?([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2$3',
            'brand_replacement' => 'Tagi',
            'model_replacement' => '$2$3',
        ],
        [
            'regex' => '@; *(Oyster500|Opal 800)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Tecmobile $1',
            'brand_replacement' => 'Tecmobile',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TECNO[ _])([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Tecno',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *Android for (Telechips|Techvision) ([^ ]+) @i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(T-Hub2)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Telstra',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(PAD) ?(100[12])(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Terra $1$2',
            'brand_replacement' => 'Terra',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *(T[BM]-\\d{3}[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Texet',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(tolino [^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Thalia',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *Build/.* (TOLINO_BROWSER)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Thalia',
            'model_replacement' => 'Tolino Shine',
        ],
        [
            'regex' => '@; *(?:CJ[ -])?(ThL|THL)[ -]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Thl',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(T100|T200|T5|W100|W200|W8s)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Thl',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(T-Mobile[ _]G2[ _]Touch) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => 'Hero',
        ],
        [
            'regex' => '@; *(T-Mobile[ _]G2) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => 'Desire Z',
        ],
        [
            'regex' => '@; *(T-Mobile myTouch Q) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => 'U8730',
        ],
        [
            'regex' => '@; *(T-Mobile myTouch) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => 'U8680',
        ],
        [
            'regex' => '@; *(T-Mobile_Espresso) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => 'Espresso',
        ],
        [
            'regex' => '@; *(T-Mobile G1) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'HTC',
            'model_replacement' => 'Dream',
        ],
        [
            'regex' => '@\\b(T-Mobile ?|)(myTouch)[ _]?([34]G)[ _]?([^\\/]*) (?:Mozilla|Build)@',
            'device_replacement' => '$1$2 $3 $4',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$2 $3 $4',
        ],
        [
            'regex' => '@\\b(T-Mobile)_([^_]+)_(.*) Build@',
            'device_replacement' => '$1 $2 $3',
            'brand_replacement' => 'Tmobile',
            'model_replacement' => '$2 $3',
        ],
        [
            'regex' => '@\\b(T-Mobile)[_ ]?(.*?)Build@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Tmobile',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@ (ATP[0-9]{4})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Tomtec',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@ *(TOOKY)[ _\\-]([^;/]+?) ?(?:Build|;)@i',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Tooky',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@\\b(TOSHIBA_AC_AND_AZ|TOSHIBA_FOLIO_AND_A|FOLIO_AND_A)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Toshiba',
            'model_replacement' => 'Folio 100',
        ],
        [
            'regex' => '@; *([Ff]olio ?100)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Toshiba',
            'model_replacement' => 'Folio 100',
        ],
        [
            'regex' => '@; *(AT[0-9]{2,3}(?:\\-A|LE\\-A|PE\\-A|SE|a|)|AT7-A|AT1S0|Hikari-iFrame/WDPF-[^;/]+|THRiVE|Thrive)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Toshiba $1',
            'brand_replacement' => 'Toshiba',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TM-MID\\d+[^;/]+|TOUCHMATE|MID-750)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Touchmate',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TM-SM\\d+[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Touchmate',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A10 [Bb]asic2?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Treq',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(TREQ[ _\\-])([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Treq',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(X-?5|X-?3)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Umeox',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(A502\\+?|A936|A603|X1|X2)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Umeox',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(TOUCH(?:TAB|PAD).+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Versus $1',
            'brand_replacement' => 'Versus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(VERTU) ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Vertu',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Videocon)[ _\\-]([^;/]+?) *(?:Build|;)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'Videocon',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@ (VT\\d{2}[A-Za-z]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Videocon',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((?:ViewPad|ViewPhone|VSD)[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Viewsonic',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(ViewSonic-)([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'Viewsonic',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(GTablet.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Viewsonic',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([Vv]ivo)[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'vivo',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@(Vodafone) (.*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(?:Walton[ _\\-]|)(Primo[ _\\-][^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Walton $1',
            'brand_replacement' => 'Walton',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:WIKO[ \\-]|)(CINK\\+?|BARRY|BLOOM|DARKFULL|DARKMOON|DARKNIGHT|DARKSIDE|FIZZ|HIGHWAY|IGGY|OZZY|RAINBOW|STAIRWAY|SUBLIM|WAX|CINK [^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Wiko $1',
            'brand_replacement' => 'Wiko',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *WellcoM-([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Wellcom $1',
            'brand_replacement' => 'Wellcom',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:(WeTab)-Browser|; (wetab) Build)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'WeTab',
            'model_replacement' => 'WeTab',
        ],
        [
            'regex' => '@; *(AT-AS[^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Wolfgang $1',
            'brand_replacement' => 'Wolfgang',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Woxter|Wxt) ([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Woxter $1',
            'brand_replacement' => 'Woxter',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Xenta |Luna |)(TAB[234][0-9]{2}|TAB0[78]-\\d{3}|TAB0?9-\\d{3}|TAB1[03]-\\d{3}|SMP\\d{2}-\\d{3})(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Yarvik $1',
            'brand_replacement' => 'Yarvik',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([A-Z]{2,4})(M\\d{3,}[A-Z]{2})([^;\\)\\/]*)(?: Build|[;\\)])@',
            'device_replacement' => 'Yifang $1$2$3',
            'brand_replacement' => 'Yifang',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *((Mi|MI|HM|MI-ONE|Redmi)[ -](NOTE |Note |)[^;/]*) (Build|MIUI)/@',
            'device_replacement' => 'XiaoMi $1',
            'brand_replacement' => 'XiaoMi',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((Mi|MI|HM|MI-ONE|Redmi)[ -](NOTE |Note |)[^;/\\)]*)@',
            'device_replacement' => 'XiaoMi $1',
            'brand_replacement' => 'XiaoMi',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(MIX) (Build|MIUI)/@',
            'device_replacement' => 'XiaoMi $1',
            'brand_replacement' => 'XiaoMi',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *((MIX) ([^;/]*)) (Build|MIUI)/@',
            'device_replacement' => 'XiaoMi $1',
            'brand_replacement' => 'XiaoMi',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *XOLO[ _]([^;/]*tab.*)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Xolo $1',
            'brand_replacement' => 'Xolo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *XOLO[ _]([^;/]+?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Xolo $1',
            'brand_replacement' => 'Xolo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(q\\d0{2,3}[a-z]?)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => 'Xolo $1',
            'brand_replacement' => 'Xolo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(PAD ?[79]\\d+[^;/]*|TelePAD\\d+[^;/])(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'Xoro $1',
            'brand_replacement' => 'Xoro',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:(?:ZOPO|Zopo)[ _]([^;/]+?)|(ZP ?(?:\\d{2}[^;/]+|C2))|(C[2379]))(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2$3',
            'brand_replacement' => 'Zopo',
            'model_replacement' => '$1$2$3',
        ],
        [
            'regex' => '@; *(ZiiLABS) (Zii[^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'ZiiLabs',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(Zii)_([^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'ZiiLabs',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(ARIZONA|(?:ATLAS|Atlas) W|D930|Grand (?:[SX][^;]*?|Era|Memo[^;]*?)|JOE|(?:Kis|KIS)\\b[^;]*?|Libra|Light [^;]*?|N8[056][01]|N850L|N8000|N9[15]\\d{2}|N9810|NX501|Optik|(?:Vip )Racer[^;]*?|RacerII|RACERII|San Francisco[^;]*?|V9[AC]|V55|V881|Z[679][0-9]{2}[A-z]?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *([A-Z]\\d+)_USA_[^;]*(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(SmartTab\\d+)[^;]*(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(?:Blade|BLADE|ZTE-BLADE)([^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'ZTE Blade$1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => 'Blade$1',
        ],
        [
            'regex' => '@; *(?:Skate|SKATE|ZTE-SKATE)([^;/]*)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'ZTE Skate$1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => 'Skate$1',
        ],
        [
            'regex' => '@; *(Orange |Optimus )(Monte Carlo|San Francisco)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1$2',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@; *(?:ZXY-ZTE_|ZTE\\-U |ZTE[\\- _]|ZTE-C[_ ])([^;/]+?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => 'ZTE $1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; (BASE) (lutea|Lutea 2|Tab[^;]*?)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@; (Avea inTouch 2|soft stone|tmn smart a7|Movistar[ _]Link)(?: Build|\\) AppleWebKit)@i',
            'device_replacement' => '$1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(vp9plus)\\)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; ?(Cloud[ _]Z5|z1000|Z99 2G|z99|z930|z999|z990|z909|Z919|z900)(?: Build|\\) AppleWebKit)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Zync',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; ?(KFOT|Kindle Fire) Build\\b@',
            'device_replacement' => 'Kindle Fire',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire',
        ],
        [
            'regex' => '@; ?(KFOTE|Amazon Kindle Fire2) Build\\b@',
            'device_replacement' => 'Kindle Fire 2',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire 2',
        ],
        [
            'regex' => '@; ?(KFTT) Build\\b@',
            'device_replacement' => 'Kindle Fire HD',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire HD 7"',
        ],
        [
            'regex' => '@; ?(KFJWI) Build\\b@',
            'device_replacement' => 'Kindle Fire HD 8.9" WiFi',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire HD 8.9" WiFi',
        ],
        [
            'regex' => '@; ?(KFJWA) Build\\b@',
            'device_replacement' => 'Kindle Fire HD 8.9" 4G',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire HD 8.9" 4G',
        ],
        [
            'regex' => '@; ?(KFSOWI) Build\\b@',
            'device_replacement' => 'Kindle Fire HD 7" WiFi',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire HD 7" WiFi',
        ],
        [
            'regex' => '@; ?(KFTHWI) Build\\b@',
            'device_replacement' => 'Kindle Fire HDX 7" WiFi',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire HDX 7" WiFi',
        ],
        [
            'regex' => '@; ?(KFTHWA) Build\\b@',
            'device_replacement' => 'Kindle Fire HDX 7" 4G',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire HDX 7" 4G',
        ],
        [
            'regex' => '@; ?(KFAPWI) Build\\b@',
            'device_replacement' => 'Kindle Fire HDX 8.9" WiFi',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire HDX 8.9" WiFi',
        ],
        [
            'regex' => '@; ?(KFAPWA) Build\\b@',
            'device_replacement' => 'Kindle Fire HDX 8.9" 4G',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire HDX 8.9" 4G',
        ],
        [
            'regex' => '@; ?Amazon ([^;/]+) Build\\b@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Amazon',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; ?(Kindle) Build\\b@',
            'device_replacement' => 'Kindle',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle',
        ],
        [
            'regex' => '@; ?(Silk)/(\\d+)\\.(\\d+)(?:\\.([0-9\\-]+)|) Build\\b@',
            'device_replacement' => 'Kindle Fire',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle Fire$2',
        ],
        [
            'regex' => '@ (Kindle)/(\\d+\\.\\d+)@',
            'device_replacement' => 'Kindle',
            'brand_replacement' => 'Amazon',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@ (Silk|Kindle)/(\\d+)\\.@',
            'device_replacement' => 'Kindle',
            'brand_replacement' => 'Amazon',
            'model_replacement' => 'Kindle',
        ],
        [
            'regex' => '@(sprd)\\-([^/]+)/@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@; *(H\\d{2}00\\+?) Build@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Hero',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(iphone|iPhone5) Build/@',
            'device_replacement' => 'Xianghe $1',
            'brand_replacement' => 'Xianghe',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; *(e\\d{4}[a-z]?_?v\\d+|v89_[^;/]+)[^;/]+ Build/@',
            'device_replacement' => 'Xianghe $1',
            'brand_replacement' => 'Xianghe',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\bUSCC[_\\-]?([^ ;/\\)]+)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Cellular',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:ALCATEL)[^;]*; *([^;,\\)]+)@',
            'device_replacement' => 'Alcatel $1',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)(?:ASUS|Asus)[^;]*; *([^;,\\)]+)@',
            'device_replacement' => 'Asus $1',
            'brand_replacement' => 'Asus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:DELL|Dell)[^;]*; *([^;,\\)]+)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)(?:HTC|Htc|HTC_blocked[^;]*)[^;]*; *(?:HTC|)([^;,\\)]+)@',
            'device_replacement' => 'HTC $1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:HUAWEI)[^;]*; *(?:HUAWEI |)([^;,\\)]+)@',
            'device_replacement' => 'Huawei $1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:LG|Lg)[^;]*; *(?:LG[ \\-]|)([^;,\\)]+)@',
            'device_replacement' => 'LG $1',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:rv:11; |)(?:NOKIA|Nokia)[^;]*; *(?:NOKIA ?|Nokia ?|LUMIA ?|[Ll]umia ?|)(\\d{3,10}[^;\\)]*)@',
            'device_replacement' => 'Lumia $1',
            'brand_replacement' => 'Nokia',
            'model_replacement' => 'Lumia $1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:NOKIA|Nokia)[^;]*; *(RM-\\d{3,})@',
            'device_replacement' => 'Nokia $1',
            'brand_replacement' => 'Nokia',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)]|WPDesktop;) ?(?:ARM; ?Touch; ?|Touch; ?|)(?:NOKIA|Nokia)[^;]*; *(?:NOKIA ?|Nokia ?|LUMIA ?|[Ll]umia ?|)([^;\\)]+)@',
            'device_replacement' => 'Nokia $1',
            'brand_replacement' => 'Nokia',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|)(?:Microsoft(?: Corporation|))[^;]*; *([^;,\\)]+)@',
            'device_replacement' => 'Microsoft $1',
            'brand_replacement' => 'Microsoft',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)(?:SAMSUNG)[^;]*; *(?:SAMSUNG |)([^;,\\.\\)]+)@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)(?:TOSHIBA|FujitsuToshibaMobileCommun)[^;]*; *([^;,\\)]+)@',
            'device_replacement' => 'Toshiba $1',
            'brand_replacement' => 'Toshiba',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Windows Phone [^;]+; .*?IEMobile/[^;\\)]+[;\\)] ?(?:ARM; ?Touch; ?|Touch; ?|WpsLondonTest; ?|)([^;]+); *([^;,\\)]+)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@(?:^|; )SAMSUNG\\-([A-Za-z0-9\\-]+).* Bada/@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\(Mobile; ALCATEL ?(One|ONE) ?(Touch|TOUCH) ?([^;/]+?)(?:/[^;]+|); rv:[^\\)]+\\) Gecko/[^\\/]+ Firefox/@',
            'device_replacement' => 'Alcatel $1 $2 $3',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => 'One Touch $3',
        ],
        [
            'regex' => '@\\(Mobile; (?:ZTE([^;]+)|(OpenC)); rv:[^\\)]+\\) Gecko/[^\\/]+ Firefox/@',
            'device_replacement' => 'ZTE $1$2',
            'brand_replacement' => 'ZTE',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@\\(Mobile; ALCATEL([A-Za-z0-9\\-]+); rv:[^\\)]+\\) Gecko/[^\\/]+ Firefox/[^\\/]+ KaiOS/@',
            'device_replacement' => 'Alcatel $1',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\(Mobile; LYF\\/([A-Za-z0-9\\-]+)\\/.+;.+rv:[^\\)]+\\) Gecko/[^\\/]+ Firefox/[^\\/]+ KAIOS/@',
            'device_replacement' => 'LYF $1',
            'brand_replacement' => 'LYF',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\(Mobile; Nokia_([A-Za-z0-9\\-]+)_.+; rv:[^\\)]+\\) Gecko/[^\\/]+ Firefox/[^\\/]+ KAIOS/@',
            'device_replacement' => 'Nokia $1',
            'brand_replacement' => 'Nokia',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Nokia(N[0-9]+)([A-Za-z_\\-][A-Za-z0-9_\\-]*)@',
            'device_replacement' => 'Nokia $1',
            'brand_replacement' => 'Nokia',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@(?:NOKIA|Nokia)(?:\\-| *)(?:([A-Za-z0-9]+)\\-[0-9a-f]{32}|([A-Za-z0-9\\-]+)(?:UCBrowser)|([A-Za-z0-9\\-]+))@',
            'device_replacement' => 'Nokia $1$2$3',
            'brand_replacement' => 'Nokia',
            'model_replacement' => '$1$2$3',
        ],
        [
            'regex' => '@Lumia ([A-Za-z0-9\\-]+)@',
            'device_replacement' => 'Lumia $1',
            'brand_replacement' => 'Nokia',
            'model_replacement' => 'Lumia $1',
        ],
        [
            'regex' => '@\\(Symbian; U; S60 V5; [A-z]{2}\\-[A-z]{2}; (SonyEricsson|Samsung|Nokia|LG)([^;/]+?)\\)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@\\(Symbian(?:/3|); U; ([^;]+);@',
            'device_replacement' => 'Nokia $1',
            'brand_replacement' => 'Nokia',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@BB10; ([A-Za-z0-9\\- ]+)\\)@',
            'device_replacement' => 'BlackBerry $1',
            'brand_replacement' => 'BlackBerry',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Play[Bb]ook.+RIM Tablet OS@',
            'device_replacement' => 'BlackBerry Playbook',
            'brand_replacement' => 'BlackBerry',
            'model_replacement' => 'Playbook',
        ],
        [
            'regex' => '@Black[Bb]erry ([0-9]+);@',
            'device_replacement' => 'BlackBerry $1',
            'brand_replacement' => 'BlackBerry',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Black[Bb]erry([0-9]+)@',
            'device_replacement' => 'BlackBerry $1',
            'brand_replacement' => 'BlackBerry',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Black[Bb]erry;@',
            'device_replacement' => 'BlackBerry',
            'brand_replacement' => 'BlackBerry',
        ],
        [
            'regex' => '@(Pre|Pixi)/\\d+\\.\\d+@',
            'device_replacement' => 'Palm $1',
            'brand_replacement' => 'Palm',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Palm([0-9]+)@',
            'device_replacement' => 'Palm $1',
            'brand_replacement' => 'Palm',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Treo([A-Za-z0-9]+)@',
            'device_replacement' => 'Palm Treo $1',
            'brand_replacement' => 'Palm',
            'model_replacement' => 'Treo $1',
        ],
        [
            'regex' => '@webOS.*(P160U(?:NA|))/(\\d+).(\\d+)@',
            'device_replacement' => 'HP Veer',
            'brand_replacement' => 'HP',
            'model_replacement' => 'Veer',
        ],
        [
            'regex' => '@(Touch[Pp]ad)/\\d+\\.\\d+@',
            'device_replacement' => 'HP TouchPad',
            'brand_replacement' => 'HP',
            'model_replacement' => 'TouchPad',
        ],
        [
            'regex' => '@HPiPAQ([A-Za-z0-9]+)/\\d+.\\d+@',
            'device_replacement' => 'HP iPAQ $1',
            'brand_replacement' => 'HP',
            'model_replacement' => 'iPAQ $1',
        ],
        [
            'regex' => '@PDA; (PalmOS)/sony/model ([a-z]+)/Revision@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Sony',
            'model_replacement' => '$1 $2',
        ],
        [
            'regex' => '@(Apple\\s?TV)@',
            'device_replacement' => 'AppleTV',
            'brand_replacement' => 'Apple',
            'model_replacement' => 'AppleTV',
        ],
        [
            'regex' => '@(QtCarBrowser)@',
            'device_replacement' => 'Tesla Model S',
            'brand_replacement' => 'Tesla',
            'model_replacement' => 'Model S',
        ],
        [
            'regex' => '@(iPhone|iPad|iPod)(\\d+,\\d+)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Apple',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@(iPad)(?:;| Simulator;)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Apple',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(iPod)(?:;| touch;| Simulator;)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Apple',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(iPhone)(?:;| Simulator;)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Apple',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(Watch)(\\d+,\\d+)@',
            'device_replacement' => 'Apple $1',
            'brand_replacement' => 'Apple',
            'model_replacement' => '$1$2',
        ],
        [
            'regex' => '@(Apple Watch)(?:;| Simulator;)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Apple',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(HomePod)(?:;| Simulator;)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Apple',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@iPhone@',
            'device_replacement' => 'iPhone',
            'brand_replacement' => 'Apple',
            'model_replacement' => 'iPhone',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/\\d.*\\(((?:Mac|iMac|PowerMac|PowerBook)[^\\d]*)(\\d+)(?:,|%2C)(\\d+)@',
            'device_replacement' => '$1$2,$3',
            'brand_replacement' => 'Apple',
            'model_replacement' => '$1$2,$3',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/\\d+\\.\\d+\\.\\d+ \\(x86_64\\)@',
            'device_replacement' => 'Mac',
            'brand_replacement' => 'Apple',
            'model_replacement' => 'Mac',
        ],
        [
            'regex' => '@CFNetwork/.* Darwin/\\d@',
            'device_replacement' => 'iOS-Device',
            'brand_replacement' => 'Apple',
            'model_replacement' => 'iOS-Device',
        ],
        [
            'regex' => '@Outlook-(iOS)/\\d+\\.\\d+\\.prod\\.iphone@',
            'brand_replacement' => 'Apple',
            'device_replacement' => 'iPhone',
            'model_replacement' => 'iPhone',
        ],
        [
            'regex' => '@acer_([A-Za-z0-9]+)_@',
            'device_replacement' => 'Acer $1',
            'brand_replacement' => 'Acer',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:ALCATEL|Alcatel)-([A-Za-z0-9\\-]+)@',
            'device_replacement' => 'Alcatel $1',
            'brand_replacement' => 'Alcatel',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:Amoi|AMOI)\\-([A-Za-z0-9]+)@',
            'device_replacement' => 'Amoi $1',
            'brand_replacement' => 'Amoi',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:; |\\/|^)((?:Transformer (?:Pad|Prime) |Transformer |PadFone[ _]?)[A-Za-z0-9]*)@',
            'device_replacement' => 'Asus $1',
            'brand_replacement' => 'Asus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:asus.*?ASUS|Asus|ASUS|asus)[\\- ;]*((?:Transformer (?:Pad|Prime) |Transformer |Padfone |Nexus[ _]|)[A-Za-z0-9]+)@',
            'device_replacement' => 'Asus $1',
            'brand_replacement' => 'Asus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:ASUS)_([A-Za-z0-9\\-]+)@',
            'device_replacement' => 'Asus $1',
            'brand_replacement' => 'Asus',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\bBIRD[ \\-\\.]([A-Za-z0-9]+)@',
            'device_replacement' => 'Bird $1',
            'brand_replacement' => 'Bird',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\bDell ([A-Za-z0-9]+)@',
            'device_replacement' => 'Dell $1',
            'brand_replacement' => 'Dell',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@DoCoMo/2\\.0 ([A-Za-z0-9]+)@',
            'device_replacement' => 'DoCoMo $1',
            'brand_replacement' => 'DoCoMo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@([A-Za-z0-9]+)_W;FOMA@',
            'device_replacement' => 'DoCoMo $1',
            'brand_replacement' => 'DoCoMo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@([A-Za-z0-9]+);FOMA@',
            'device_replacement' => 'DoCoMo $1',
            'brand_replacement' => 'DoCoMo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@\\b(?:HTC/|HTC/[a-z0-9]+/|)HTC[ _\\-;]? *(.*?)(?:-?Mozilla|fingerPrint|[;/\\(\\)]|$)@',
            'device_replacement' => 'HTC $1',
            'brand_replacement' => 'HTC',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Huawei([A-Za-z0-9]+)@',
            'device_replacement' => 'Huawei $1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@HUAWEI-([A-Za-z0-9]+)@',
            'device_replacement' => 'Huawei $1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@HUAWEI ([A-Za-z0-9\\-]+)@',
            'device_replacement' => 'Huawei $1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@vodafone([A-Za-z0-9]+)@',
            'device_replacement' => 'Huawei Vodafone $1',
            'brand_replacement' => 'Huawei',
            'model_replacement' => 'Vodafone $1',
        ],
        [
            'regex' => '@i\\-mate ([A-Za-z0-9]+)@',
            'device_replacement' => 'i-mate $1',
            'brand_replacement' => 'i-mate',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Kyocera\\-([A-Za-z0-9]+)@',
            'device_replacement' => 'Kyocera $1',
            'brand_replacement' => 'Kyocera',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@KWC\\-([A-Za-z0-9]+)@',
            'device_replacement' => 'Kyocera $1',
            'brand_replacement' => 'Kyocera',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Lenovo[_\\-]([A-Za-z0-9]+)@',
            'device_replacement' => 'Lenovo $1',
            'brand_replacement' => 'Lenovo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(HbbTV)/[0-9]+\\.[0-9]+\\.[0-9]+ \\( ?;(LG)E ?;([^;]{0,30})@',
            'device_replacement' => '$1',
            'brand_replacement' => '$2',
            'model_replacement' => '$3',
        ],
        [
            'regex' => '@(HbbTV)/1\\.1\\.1.*CE-HTML/1\\.\\d;(Vendor/|)(THOM[^;]*?)[;\\s].{0,30}(LF[^;]+);?@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Thomson',
            'model_replacement' => '$4',
        ],
        [
            'regex' => '@(HbbTV)(?:/1\\.1\\.1|) ?(?: \\(;;;;;\\)|); *CE-HTML(?:/1\\.\\d|); *([^ ]+) ([^;]+);@',
            'device_replacement' => '$1',
            'brand_replacement' => '$2',
            'model_replacement' => '$3',
        ],
        [
            'regex' => '@(HbbTV)/1\\.1\\.1 \\(;;;;;\\) Maple_2011@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Samsung',
        ],
        [
            'regex' => '@(HbbTV)/[0-9]+\\.[0-9]+\\.[0-9]+ \\([^;]{0,30}; ?(?:CUS:([^;]*)|([^;]+)) ?; ?([^;]{0,30})@',
            'device_replacement' => '$1',
            'brand_replacement' => '$2$3',
            'model_replacement' => '$4',
        ],
        [
            'regex' => '@(HbbTV)/[0-9]+\\.[0-9]+\\.[0-9]+@',
            'device_replacement' => '$1',
        ],
        [
            'regex' => '@LGE; (?:Media\\/|)([^;]*);[^;]*;[^;]*;?\\); "?LG NetCast(\\.TV|\\.Media|)-\\d+@',
            'device_replacement' => 'NetCast$2',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@InettvBrowser/[0-9]+\\.[0-9A-Z]+ \\([^;]*;(Sony)([^;]*);[^;]*;[^\\)]*\\)@',
            'device_replacement' => 'Inettv',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@InettvBrowser/[0-9]+\\.[0-9A-Z]+ \\([^;]*;([^;]*);[^;]*;[^\\)]*\\)@',
            'device_replacement' => 'Inettv',
            'brand_replacement' => 'Generic_Inettv',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:InettvBrowser|TSBNetTV|NETTV|HBBTV)@',
            'device_replacement' => 'Inettv',
            'brand_replacement' => 'Generic_Inettv',
        ],
        [
            'regex' => '@Series60/\\d\\.\\d (LG)[\\-]?([A-Za-z0-9 \\-]+)@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@\\b(?:LGE[ \\-]LG\\-(?:AX|)|LGE |LGE?-LG|LGE?[ \\-]|LG[ /\\-]|lg[\\-])([A-Za-z0-9]+)\\b@',
            'device_replacement' => 'LG $1',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:^LG[\\-]?|^LGE[\\-/]?)([A-Za-z]+[0-9]+[A-Za-z]*)@',
            'device_replacement' => 'LG $1',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@^LG([0-9]+[A-Za-z]*)@',
            'device_replacement' => 'LG $1',
            'brand_replacement' => 'LG',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(KIN\\.[^ ]+) (\\d+)\\.(\\d+)@',
            'device_replacement' => 'Microsoft $1',
            'brand_replacement' => 'Microsoft',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:MSIE|XBMC).*\\b(Xbox)\\b@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Microsoft',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@; ARM; Trident/6\\.0; Touch[\\);]@',
            'device_replacement' => 'Microsoft Surface RT',
            'brand_replacement' => 'Microsoft',
            'model_replacement' => 'Surface RT',
        ],
        [
            'regex' => '@Motorola\\-([A-Za-z0-9]+)@',
            'device_replacement' => 'Motorola $1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@MOTO\\-([A-Za-z0-9]+)@',
            'device_replacement' => 'Motorola $1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@MOT\\-([A-z0-9][A-z0-9\\-]*)@',
            'device_replacement' => 'Motorola $1',
            'brand_replacement' => 'Motorola',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Nintendo WiiU@',
            'device_replacement' => 'Nintendo Wii U',
            'brand_replacement' => 'Nintendo',
            'model_replacement' => 'Wii U',
        ],
        [
            'regex' => '@Nintendo (DS|3DS|DSi|Wii);@',
            'device_replacement' => 'Nintendo $1',
            'brand_replacement' => 'Nintendo',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(?:Pantech|PANTECH)[ _-]?([A-Za-z0-9\\-]+)@',
            'device_replacement' => 'Pantech $1',
            'brand_replacement' => 'Pantech',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Philips([A-Za-z0-9]+)@',
            'device_replacement' => 'Philips $1',
            'brand_replacement' => 'Philips',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Philips ([A-Za-z0-9]+)@',
            'device_replacement' => 'Philips $1',
            'brand_replacement' => 'Philips',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(SMART-TV); .* Tizen @',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@SymbianOS/9\\.\\d.* Samsung[/\\-]([A-Za-z0-9 \\-]+)@',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(Samsung)(SGH)(i[0-9]+)@',
            'device_replacement' => '$1 $2$3',
            'brand_replacement' => '$1',
            'model_replacement' => '$2-$3',
        ],
        [
            'regex' => '@SAMSUNG-ANDROID-MMS/([^;/]+)@',
            'device_replacement' => '$1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@SAMSUNG(?:; |[ -/])([A-Za-z0-9\\-]+)@i',
            'device_replacement' => 'Samsung $1',
            'brand_replacement' => 'Samsung',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(Dreamcast)@',
            'device_replacement' => 'Sega $1',
            'brand_replacement' => 'Sega',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@^SIE-([A-Za-z0-9]+)@',
            'device_replacement' => 'Siemens $1',
            'brand_replacement' => 'Siemens',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Softbank/[12]\\.0/([A-Za-z0-9]+)@',
            'device_replacement' => 'Softbank $1',
            'brand_replacement' => 'Softbank',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@SonyEricsson ?([A-Za-z0-9\\-]+)@',
            'device_replacement' => 'Ericsson $1',
            'brand_replacement' => 'SonyEricsson',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android [^;]+; ([^ ]+) (Sony)/@',
            'device_replacement' => '$2 $1',
            'brand_replacement' => '$2',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(Sony)(?:BDP\\/|\\/|)([^ /;\\)]+)[ /;\\)]@',
            'device_replacement' => '$1 $2',
            'brand_replacement' => '$1',
            'model_replacement' => '$2',
        ],
        [
            'regex' => '@Puffin/[\\d\\.]+IT@',
            'device_replacement' => 'iPad',
            'brand_replacement' => 'Apple',
            'model_replacement' => 'iPad',
        ],
        [
            'regex' => '@Puffin/[\\d\\.]+IP@',
            'device_replacement' => 'iPhone',
            'brand_replacement' => 'Apple',
            'model_replacement' => 'iPhone',
        ],
        [
            'regex' => '@Puffin/[\\d\\.]+AT@',
            'device_replacement' => 'Generic Tablet',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Tablet',
        ],
        [
            'regex' => '@Puffin/[\\d\\.]+AP@',
            'device_replacement' => 'Generic Smartphone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Smartphone',
        ],
        [
            'regex' => '@Android[\\- ][\\d]+\\.[\\d]+; [A-Za-z]{2}\\-[A-Za-z]{0,2}; WOWMobile (.+)( Build[/ ]|\\))@',
            'brand_replacement' => 'Generic_Android',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android[\\- ][\\d]+\\.[\\d]+\\-update1; [A-Za-z]{2}\\-[A-Za-z]{0,2} *; *(.+?)( Build[/ ]|\\))@',
            'brand_replacement' => 'Generic_Android',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android[\\- ][\\d]+(?:\\.[\\d]+)(?:\\.[\\d]+|); *[A-Za-z]{2}[_\\-][A-Za-z]{0,2}\\-? *; *(.+?)( Build[/ ]|\\))@',
            'brand_replacement' => 'Generic_Android',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android[\\- ][\\d]+(?:\\.[\\d]+)(?:\\.[\\d]+|); *[A-Za-z]{0,2}\\- *; *(.+?)( Build[/ ]|\\))@',
            'brand_replacement' => 'Generic_Android',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android[\\- ][\\d]+(?:\\.[\\d]+)(?:\\.[\\d]+|); *[a-z]{0,2}[_\\-]?[A-Za-z]{0,2};?( Build[/ ]|\\))@',
            'device_replacement' => 'Generic Smartphone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Smartphone',
        ],
        [
            'regex' => '@Android[\\- ][\\d]+(?:\\.[\\d]+)(?:\\.[\\d]+|); *\\-?[A-Za-z]{2}; *(.+?)( Build[/ ]|\\))@',
            'brand_replacement' => 'Generic_Android',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android \\d+?(?:\\.\\d+|)(?:\\.\\d+|); ([^;]+?)(?: Build|\\) AppleWebKit).+? Mobile Safari@',
            'brand_replacement' => 'Generic_Android',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android \\d+?(?:\\.\\d+|)(?:\\.\\d+|); ([^;]+?)(?: Build|\\) AppleWebKit).+? Safari@',
            'brand_replacement' => 'Generic_Android_Tablet',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@Android \\d+?(?:\\.\\d+|)(?:\\.\\d+|); ([^;]+?)(?: Build|\\))@',
            'brand_replacement' => 'Generic_Android',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(GoogleTV)@',
            'brand_replacement' => 'Generic_Inettv',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(WebTV)/\\d+.\\d+@',
            'brand_replacement' => 'Generic_Inettv',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@^(Roku)/DVP-\\d+\\.\\d+@',
            'brand_replacement' => 'Generic_Inettv',
            'model_replacement' => '$1',
        ],
        [
            'regex' => '@(Android 3\\.\\d|Opera Tablet|Tablet; .+Firefox/|Android.*(?:Tab|Pad))@i',
            'device_replacement' => 'Generic Tablet',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Tablet',
        ],
        [
            'regex' => '@(Symbian|\\bS60(Version|V\\d)|\\bS60\\b|\\((Series 60|Windows Mobile|Palm OS|Bada); Opera Mini|Windows CE|Opera Mobi|BREW|Brew|Mobile; .+Firefox/|iPhone OS|Android|MobileSafari|Windows *Phone|\\(webOS/|PalmOS)@',
            'device_replacement' => 'Generic Smartphone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Smartphone',
        ],
        [
            'regex' => '@(hiptop|avantgo|plucker|xiino|blazer|elaine)@i',
            'device_replacement' => 'Generic Smartphone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Smartphone',
        ],
        [
            'regex' => '@(bot|BUbiNG|zao|borg|DBot|oegp|silk|Xenu|zeal|^NING|CCBot|crawl|htdig|lycos|slurp|teoma|voila|yahoo|Sogou|CiBra|Nutch|^Java/|^JNLP/|Daumoa|Daum|Genieo|ichiro|larbin|pompos|Scrapy|snappy|speedy|spider|msnbot|msrbot|vortex|^vortex|crawler|favicon|indexer|Riddler|scooter|scraper|scrubby|WhatWeb|WinHTTP|bingbot|BingPreview|openbot|gigabot|furlbot|polybot|seekbot|^voyager|archiver|Icarus6j|mogimogi|Netvibes|blitzbot|altavista|charlotte|findlinks|Retreiver|TLSProber|WordPress|SeznamBot|ProoXiBot|wsr\\-agent|Squrl Java|EtaoSpider|PaperLiBot|SputnikBot|A6\\-Indexer|netresearch|searchsight|baiduspider|YisouSpider|ICC\\-Crawler|http%20client|Python-urllib|dataparksearch|converacrawler|Screaming Frog|AppEngine-Google|YahooCacheSystem|fast\\-webcrawler|Sogou Pic Spider|semanticdiscovery|Innovazion Crawler|facebookexternalhit|Google.*/\\+/web/snippet|Google-HTTP-Java-Client|BlogBridge|IlTrovatore-Setaccio|InternetArchive|GomezAgent|WebThumbnail|heritrix|NewsGator|PagePeeker|Reaper|ZooShot|holmes|NL-Crawler|Pingdom|StatusCake|WhatsApp|masscan|Google Web Preview|Qwantify|Yeti|OgScrper)@i',
            'device_replacement' => 'Spider',
            'brand_replacement' => 'Spider',
            'model_replacement' => 'Desktop',
        ],
        [
            'regex' => '@^(1207|3gso|4thp|501i|502i|503i|504i|505i|506i|6310|6590|770s|802s|a wa|acer|acs\\-|airn|alav|asus|attw|au\\-m|aur |aus |abac|acoo|aiko|alco|alca|amoi|anex|anny|anyw|aptu|arch|argo|bmobile|bell|bird|bw\\-n|bw\\-u|beck|benq|bilb|blac|c55/|cdm\\-|chtm|capi|comp|cond|dall|dbte|dc\\-s|dica|ds\\-d|ds12|dait|devi|dmob|doco|dopo|dorado|el(?:38|39|48|49|50|55|58|68)|el[3456]\\d{2}dual|erk0|esl8|ex300|ez40|ez60|ez70|ezos|ezze|elai|emul|eric|ezwa|fake|fly\\-|fly_|g\\-mo|g1 u|g560|gf\\-5|grun|gene|go.w|good|grad|hcit|hd\\-m|hd\\-p|hd\\-t|hei\\-|hp i|hpip|hs\\-c|htc |htc\\-|htca|htcg)@i',
            'device_replacement' => 'Generic Feature Phone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Feature Phone',
        ],
        [
            'regex' => '@^(htcp|htcs|htct|htc_|haie|hita|huaw|hutc|i\\-20|i\\-go|i\\-ma|i\\-mobile|i230|iac|iac\\-|iac/|ig01|im1k|inno|iris|jata|kddi|kgt|kgt/|kpt |kwc\\-|klon|lexi|lg g|lg\\-a|lg\\-b|lg\\-c|lg\\-d|lg\\-f|lg\\-g|lg\\-k|lg\\-l|lg\\-m|lg\\-o|lg\\-p|lg\\-s|lg\\-t|lg\\-u|lg\\-w|lg/k|lg/l|lg/u|lg50|lg54|lge\\-|lge/|leno|m1\\-w|m3ga|m50/|maui|mc01|mc21|mcca|medi|meri|mio8|mioa|mo01|mo02|mode|modo|mot |mot\\-|mt50|mtp1|mtv |mate|maxo|merc|mits|mobi|motv|mozz|n100|n101|n102|n202|n203|n300|n302|n500|n502|n505|n700|n701|n710|nec\\-|nem\\-|newg|neon)@i',
            'device_replacement' => 'Generic Feature Phone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Feature Phone',
        ],
        [
            'regex' => '@^(netf|noki|nzph|o2 x|o2\\-x|opwv|owg1|opti|oran|ot\\-s|p800|pand|pg\\-1|pg\\-2|pg\\-3|pg\\-6|pg\\-8|pg\\-c|pg13|phil|pn\\-2|pt\\-g|palm|pana|pire|pock|pose|psio|qa\\-a|qc\\-2|qc\\-3|qc\\-5|qc\\-7|qc07|qc12|qc21|qc32|qc60|qci\\-|qwap|qtek|r380|r600|raks|rim9|rove|s55/|sage|sams|sc01|sch\\-|scp\\-|sdk/|se47|sec\\-|sec0|sec1|semc|sgh\\-|shar|sie\\-|sk\\-0|sl45|slid|smb3|smt5|sp01|sph\\-|spv |spv\\-|sy01|samm|sany|sava|scoo|send|siem|smar|smit|soft|sony|t\\-mo|t218|t250|t600|t610|t618|tcl\\-|tdg\\-|telm|tim\\-|ts70|tsm\\-|tsm3|tsm5|tx\\-9|tagt)@i',
            'device_replacement' => 'Generic Feature Phone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Feature Phone',
        ],
        [
            'regex' => '@^(talk|teli|topl|tosh|up.b|upg1|utst|v400|v750|veri|vk\\-v|vk40|vk50|vk52|vk53|vm40|vx98|virg|vertu|vite|voda|vulc|w3c |w3c\\-|wapj|wapp|wapu|wapm|wig |wapi|wapr|wapv|wapy|wapa|waps|wapt|winc|winw|wonu|x700|xda2|xdag|yas\\-|your|zte\\-|zeto|aste|audi|avan|blaz|brew|brvw|bumb|ccwa|cell|cldc|cmd\\-|dang|eml2|fetc|hipt|http|ibro|idea|ikom|ipaq|jbro|jemu|jigs|keji|kyoc|kyok|libw|m\\-cr|midp|mmef|moto|mwbp|mywa|newt|nok6|o2im|pant|pdxg|play|pluc|port|prox|rozo|sama|seri|smal|symb|treo|upsi|vx52|vx53|vx60|vx61|vx70|vx80|vx81|vx83|vx85|wap\\-|webc|whit|wmlb|xda\\-|xda_)@i',
            'device_replacement' => 'Generic Feature Phone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Feature Phone',
        ],
        [
            'regex' => '@^(Ice)$@',
            'device_replacement' => 'Generic Feature Phone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Feature Phone',
        ],
        [
            'regex' => '@(wap[\\-\\ ]browser|maui|netfront|obigo|teleca|up\\.browser|midp|Opera Mini)@i',
            'device_replacement' => 'Generic Feature Phone',
            'brand_replacement' => 'Generic',
            'model_replacement' => 'Feature Phone',
        ],
        [
            'regex' => '@Mac OS@',
            'device_replacement' => 'Mac',
            'brand_replacement' => 'Apple',
            'model_replacement' => 'Mac',
        ],
    ],
];
