<?php

declare(strict_types=1);

namespace GrumPHP\Runner\TaskHandler\Middleware;

use Amp\Promise;
use <PERSON><PERSON><PERSON><PERSON>\Runner\TaskResultInterface;
use <PERSON><PERSON><PERSON><PERSON>\Runner\TaskRunnerContext;
use <PERSON>rum<PERSON><PERSON>\Task\TaskInterface;

interface TaskHandlerMiddlewareInterface
{
    /**
     * @param callable(TaskInterface, TaskRunnerContext): Promise<TaskResultInterface> $next
     * @return Promise<TaskResultInterface>
     */
    public function handle(
        TaskInterface $task,
        TaskRunnerContext $runnerContext,
        callable $next
    ): Promise;
}
