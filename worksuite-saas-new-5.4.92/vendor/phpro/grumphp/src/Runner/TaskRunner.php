<?php

declare(strict_types=1);

namespace GrumPHP\Runner;

use GrumP<PERSON>\Collection\TaskResultCollection;
use GrumP<PERSON>\Collection\TasksCollection;

class TaskRunner
{
    /**
     * @var TasksCollection
     */
    private $tasks;

    /**
     * @var MiddlewareStack
     */
    private $middleware;

    public function __construct(TasksCollection $tasks, MiddlewareStack $middleware)
    {
        $this->tasks = $tasks;
        $this->middleware = $middleware;
    }

    public function run(TaskRunnerContext $runnerContext): TaskResultCollection
    {
        return $this->middleware->handle(
            $runnerContext->withTasks($this->tasks)
        );
    }
}
