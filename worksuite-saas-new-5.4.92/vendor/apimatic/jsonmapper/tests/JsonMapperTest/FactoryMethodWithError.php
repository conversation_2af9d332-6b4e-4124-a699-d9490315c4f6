<?php
/**
 * Part of JsonMapper
 *
 * PHP version 5
 *
 * @package  JsonMapper
 * <AUTHOR> <<EMAIL>>
 * @license  OSL-3.0 http://opensource.org/licenses/osl-3.0
 * @link     https://apimatic.io/
 */

/**
 * Unit test helper class for testing property mapping
 *
 * @package  JsonMapper
 * <AUTHOR> <<EMAIL>>
 * @license  OSL-3.0 http://opensource.org/licenses/osl-3.0
 * @link     https://apimatic.io/
 */
class FactoryMethodWithError
{
    /**
     * @factory NonExistentMethod
     */
    public $simple;
}
?>
