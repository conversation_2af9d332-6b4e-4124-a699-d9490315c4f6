{"name": "apimatic/jsonmapper", "description": "Map nested JSON structures onto PHP classes", "license": "OSL-3.0", "autoload": {"psr-4": {"apimatic\\jsonmapper\\": "src/"}}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.netresearch.de/", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://apimatic.io/", "role": "Developer"}], "support": {"email": "<EMAIL>", "issues": "https://github.com/apimatic/jsonmapper/issues"}, "require-dev": {"squizlabs/php_codesniffer": "^3.0.0", "phpunit/phpunit": "^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0"}}