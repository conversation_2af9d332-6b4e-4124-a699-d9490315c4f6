{"name": "phpstan/phpdoc-parser", "description": "PHPDoc parser with support for nullable, intersection and generic types", "license": "MIT", "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^5.3.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.6", "symfony/process": "^5.2"}, "config": {"platform": {"php": "7.4.6"}, "sort-packages": true, "allow-plugins": {"phpstan/extension-installer": true}}, "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "autoload-dev": {"psr-4": {"PHPStan\\PhpDocParser\\": ["tests/PHPStan"]}}, "minimum-stability": "dev", "prefer-stable": true}