{"name": "macsidigital/laravel-api-client", "description": "Laravel API Client Builder package", "keywords": ["macsidigital", "laravel-api-client"], "homepage": "https://github.com/macsidigital/laravel-api-client", "license": "MIT", "type": "library", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.3|^8.0|^8.1", "nesbot/carbon": "^1.26.3 || ^2.0", "guzzlehttp/guzzle": "~7.0|~6.0|~5.0|~4.0", "guzzlehttp/oauth-subscriber": "^0.6.0", "firebase/php-jwt": "^6.0", "illuminate/support": "^7.0|^8.0|^9.0|^10.0", "macsidigital/laravel-oauth2-client": "^1.2|^2.0"}, "require-dev": {"orchestra/testbench": "^4.0|^5.0|^6.0|^7.0", "phpunit/phpunit": "^8.0|^9.0|^10.0"}, "autoload": {"psr-4": {"MacsiDigital\\API\\": "src"}}, "autoload-dev": {"psr-4": {"MacsiDigital\\API\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes"}, "config": {"sort-packages": true}, "extra": {"laravel": {"providers": ["MacsiDigital\\API\\Providers\\APIServiceProvider"], "aliases": {}}}}