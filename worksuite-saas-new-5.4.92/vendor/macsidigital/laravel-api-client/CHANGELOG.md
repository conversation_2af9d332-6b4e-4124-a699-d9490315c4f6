# Changelog

All notable changes to `laravel-api-client` will be documented in this file

## 3.3.0 - 2021-01-05

- PHP 8.0

## 3.1.0 - 2020-09-08

- Laravel 8.0

## 3.0.4 - 2020-05-29

- Bug fix, getKey function went into infinite loop

## 3.0.3 - 2020-05-29

- Bug fix, error on deletion.

## 3.0.2 - 2020-05-29

- Bug fix, on save or deletion the populated model was not being returned.

## 3.0.0 - 2020-05-27

- The Laravel 7.0 Version
- Use the Laravel Http client.

## 2.0.5 - 2020-05-29

- Bug fix, getKey function went into infinite loop

## 2.0.4 - 2020-05-29

- Bug fix, error on deletion.

## 2.0.3 - 2020-05-29

- Bug fix, on save or deletion the populated model was not being returned.

## 2.0.0 - 2020-05-27

- The Laravel 6.0 Version
- Mainly updates to use newer Laravel Model Attribute casting.

## 1.0.17 - 2020-05-29

- Bug fix, getKey function went into infinite loop

## 1.0.16 - 2020-05-29

- Bug fix, error on deletion.

## 1.0.14 - 2020-05-29

- Bug fix, on save or deletion the populated model was not being returned.

## 1.0.12 - 2020-05-27

- Updates to many things.

## 1.0.11 - 2020-05-18

- Initial Public Release

## There are many 1.0 early versions which we wont document, we had to create them as composer wasnt allowing us to work locally.

## 0.0.1 - 2020-03-11

- New API Builder Library
