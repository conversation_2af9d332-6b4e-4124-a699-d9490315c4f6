{"name": "macsidigital/laravel-oauth2-client", "description": "Laravel OAuth2 Client", "keywords": ["macsidigital", "laravel-oauth2-client"], "homepage": "https://github.com/macsidigital/laravel-oauth2-client", "license": "MIT", "type": "library", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "require": {"php": "^7.2|^8.0|^8.1", "nesbot/carbon": "^1.26.3 || ^2.0", "illuminate/support": "^7.0|^8.0|^9.0|^10.0", "league/oauth2-client": "^2.0"}, "require-dev": {"orchestra/testbench": "^5.0|^6.0|^7.0", "phpunit/phpunit": "^8.0|^9.0|^10.0"}, "autoload": {"psr-4": {"MacsiDigital\\OAuth2\\": "src"}}, "autoload-dev": {"psr-4": {"MacsiDigital\\OAuth2\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes"}, "config": {"sort-packages": true}, "extra": {"laravel": {"providers": ["MacsiDigital\\OAuth2\\Providers\\OAuth2ServiceProvider"], "aliases": {}}}, "minimum-stability": "dev", "prefer-stable": true}