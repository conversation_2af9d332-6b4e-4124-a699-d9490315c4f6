{"name": "macsidigital/laravel-zoom", "description": "Laravel Zoom package", "homepage": "https://github.com/MacsiDigital/laravel-zoom", "keywords": ["macsidigital", "laravel-zoom", "zoom", "laravel", "api", "client"], "license": "MIT", "type": "library", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": "^8.0|^8.1", "illuminate/support": "^8.0|^9.0|^10.0", "macsidigital/laravel-api-client": "^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16|^3.14", "orchestra/testbench": "^6.0|^7.0|^8.0", "phpunit/phpunit": "^9.0"}, "autoload": {"psr-4": {"MacsiDigital\\Zoom\\": "src"}}, "autoload-dev": {"psr-4": {"MacsiDigital\\Zoom\\Tests\\": "tests"}}, "scripts": {"test": "vendor/bin/phpunit", "test-coverage": "vendor/bin/phpunit --coverage-html coverage", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes"}, "config": {"sort-packages": true}, "extra": {"laravel": {"providers": ["MacsiDigital\\Zoom\\Providers\\ZoomServiceProvider"], "aliases": {"Zoom": "MacsiDigital\\Zoom\\Facades\\Zoom"}}}, "minimum-stability": "dev", "prefer-stable": true}